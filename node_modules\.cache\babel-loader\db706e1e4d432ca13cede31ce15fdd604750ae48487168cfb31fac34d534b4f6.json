{"ast": null, "code": "import { gapi } from 'gapi-script';\nimport { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';\nclass GoogleAuthService {\n  constructor() {\n    this.isInitialized = false;\n    this.authInstance = null;\n  }\n\n  // Initialize Google API\n  async initializeGapi() {\n    if (this.isInitialized) {\n      return this.authInstance;\n    }\n    try {\n      await new Promise((resolve, reject) => {\n        gapi.load('auth2', {\n          callback: resolve,\n          onerror: reject\n        });\n      });\n      this.authInstance = await gapi.auth2.init({\n        client_id: GOOGLE_OAUTH_CONFIG.clientId,\n        scope: GOOGLE_OAUTH_CONFIG.scope\n      });\n      this.isInitialized = true;\n      return this.authInstance;\n    } catch (error) {\n      console.error('Error initializing Google API:', error);\n      throw new Error('Failed to initialize Google authentication');\n    }\n  }\n\n  // Sign in with Google\n  async signIn() {\n    try {\n      if (!this.isInitialized) {\n        await this.initializeGapi();\n      }\n      const authResult = await this.authInstance.signIn();\n      const profile = authResult.getBasicProfile();\n      const authResponse = authResult.getAuthResponse();\n      return {\n        success: true,\n        user: {\n          id: profile.getId(),\n          name: profile.getName(),\n          email: profile.getEmail(),\n          imageUrl: profile.getImageUrl()\n        },\n        token: authResponse.id_token,\n        accessToken: authResponse.access_token\n      };\n    } catch (error) {\n      console.error('Google sign-in error:', error);\n      return {\n        success: false,\n        error: error.error || 'Google sign-in failed'\n      };\n    }\n  }\n\n  // Sign out\n  async signOut() {\n    try {\n      if (this.authInstance) {\n        await this.authInstance.signOut();\n      }\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Google sign-out error:', error);\n      return {\n        success: false,\n        error: 'Sign-out failed'\n      };\n    }\n  }\n\n  // Check if user is signed in\n  isSignedIn() {\n    if (!this.authInstance) {\n      return false;\n    }\n    return this.authInstance.isSignedIn.get();\n  }\n\n  // Get current user\n  getCurrentUser() {\n    if (!this.authInstance || !this.isSignedIn()) {\n      return null;\n    }\n    const user = this.authInstance.currentUser.get();\n    const profile = user.getBasicProfile();\n    return {\n      id: profile.getId(),\n      name: profile.getName(),\n      email: profile.getEmail(),\n      imageUrl: profile.getImageUrl()\n    };\n  }\n}\n\n// Create and export a singleton instance\nconst googleAuthService = new GoogleAuthService();\nexport default googleAuthService;", "map": {"version": 3, "names": ["<PERSON>i", "GOOGLE_OAUTH_CONFIG", "GoogleAuthService", "constructor", "isInitialized", "authInstance", "initializeGapi", "Promise", "resolve", "reject", "load", "callback", "onerror", "auth2", "init", "client_id", "clientId", "scope", "error", "console", "Error", "signIn", "authResult", "profile", "getBasicProfile", "authResponse", "getAuthResponse", "success", "user", "id", "getId", "name", "getName", "email", "getEmail", "imageUrl", "getImageUrl", "token", "id_token", "accessToken", "access_token", "signOut", "isSignedIn", "get", "getCurrentUser", "currentUser", "googleAuthService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/services/googleAuthService.js"], "sourcesContent": ["import { gapi } from 'gapi-script';\nimport { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';\n\nclass GoogleAuthService {\n  constructor() {\n    this.isInitialized = false;\n    this.authInstance = null;\n  }\n\n  // Initialize Google API\n  async initializeGapi() {\n    if (this.isInitialized) {\n      return this.authInstance;\n    }\n\n    try {\n      await new Promise((resolve, reject) => {\n        gapi.load('auth2', {\n          callback: resolve,\n          onerror: reject,\n        });\n      });\n\n      this.authInstance = await gapi.auth2.init({\n        client_id: GOOGLE_OAUTH_CONFIG.clientId,\n        scope: GOOGLE_OAUTH_CONFIG.scope,\n      });\n\n      this.isInitialized = true;\n      return this.authInstance;\n    } catch (error) {\n      console.error('Error initializing Google API:', error);\n      throw new Error('Failed to initialize Google authentication');\n    }\n  }\n\n  // Sign in with Google\n  async signIn() {\n    try {\n      if (!this.isInitialized) {\n        await this.initializeGapi();\n      }\n\n      const authResult = await this.authInstance.signIn();\n      const profile = authResult.getBasicProfile();\n      const authResponse = authResult.getAuthResponse();\n\n      return {\n        success: true,\n        user: {\n          id: profile.getId(),\n          name: profile.getName(),\n          email: profile.getEmail(),\n          imageUrl: profile.getImageUrl(),\n        },\n        token: authResponse.id_token,\n        accessToken: authResponse.access_token,\n      };\n    } catch (error) {\n      console.error('Google sign-in error:', error);\n      return {\n        success: false,\n        error: error.error || 'Google sign-in failed',\n      };\n    }\n  }\n\n  // Sign out\n  async signOut() {\n    try {\n      if (this.authInstance) {\n        await this.authInstance.signOut();\n      }\n      return { success: true };\n    } catch (error) {\n      console.error('Google sign-out error:', error);\n      return { success: false, error: 'Sign-out failed' };\n    }\n  }\n\n  // Check if user is signed in\n  isSignedIn() {\n    if (!this.authInstance) {\n      return false;\n    }\n    return this.authInstance.isSignedIn.get();\n  }\n\n  // Get current user\n  getCurrentUser() {\n    if (!this.authInstance || !this.isSignedIn()) {\n      return null;\n    }\n\n    const user = this.authInstance.currentUser.get();\n    const profile = user.getBasicProfile();\n    \n    return {\n      id: profile.getId(),\n      name: profile.getName(),\n      email: profile.getEmail(),\n      imageUrl: profile.getImageUrl(),\n    };\n  }\n}\n\n// Create and export a singleton instance\nconst googleAuthService = new GoogleAuthService();\nexport default googleAuthService;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,aAAa;AAClC,SAASC,mBAAmB,QAAQ,uBAAuB;AAE3D,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;EAC1B;;EAEA;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACF,aAAa,EAAE;MACtB,OAAO,IAAI,CAACC,YAAY;IAC1B;IAEA,IAAI;MACF,MAAM,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACrCT,IAAI,CAACU,IAAI,CAAC,OAAO,EAAE;UACjBC,QAAQ,EAAEH,OAAO;UACjBI,OAAO,EAAEH;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAACJ,YAAY,GAAG,MAAML,IAAI,CAACa,KAAK,CAACC,IAAI,CAAC;QACxCC,SAAS,EAAEd,mBAAmB,CAACe,QAAQ;QACvCC,KAAK,EAAEhB,mBAAmB,CAACgB;MAC7B,CAAC,CAAC;MAEF,IAAI,CAACb,aAAa,GAAG,IAAI;MACzB,OAAO,IAAI,CAACC,YAAY;IAC1B,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;IAC/D;EACF;;EAEA;EACA,MAAMC,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,IAAI,CAAC,IAAI,CAACjB,aAAa,EAAE;QACvB,MAAM,IAAI,CAACE,cAAc,CAAC,CAAC;MAC7B;MAEA,MAAMgB,UAAU,GAAG,MAAM,IAAI,CAACjB,YAAY,CAACgB,MAAM,CAAC,CAAC;MACnD,MAAME,OAAO,GAAGD,UAAU,CAACE,eAAe,CAAC,CAAC;MAC5C,MAAMC,YAAY,GAAGH,UAAU,CAACI,eAAe,CAAC,CAAC;MAEjD,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJC,EAAE,EAAEN,OAAO,CAACO,KAAK,CAAC,CAAC;UACnBC,IAAI,EAAER,OAAO,CAACS,OAAO,CAAC,CAAC;UACvBC,KAAK,EAAEV,OAAO,CAACW,QAAQ,CAAC,CAAC;UACzBC,QAAQ,EAAEZ,OAAO,CAACa,WAAW,CAAC;QAChC,CAAC;QACDC,KAAK,EAAEZ,YAAY,CAACa,QAAQ;QAC5BC,WAAW,EAAEd,YAAY,CAACe;MAC5B,CAAC;IACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QACLS,OAAO,EAAE,KAAK;QACdT,KAAK,EAAEA,KAAK,CAACA,KAAK,IAAI;MACxB,CAAC;IACH;EACF;;EAEA;EACA,MAAMuB,OAAOA,CAAA,EAAG;IACd,IAAI;MACF,IAAI,IAAI,CAACpC,YAAY,EAAE;QACrB,MAAM,IAAI,CAACA,YAAY,CAACoC,OAAO,CAAC,CAAC;MACnC;MACA,OAAO;QAAEd,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QAAES,OAAO,EAAE,KAAK;QAAET,KAAK,EAAE;MAAkB,CAAC;IACrD;EACF;;EAEA;EACAwB,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACrC,YAAY,EAAE;MACtB,OAAO,KAAK;IACd;IACA,OAAO,IAAI,CAACA,YAAY,CAACqC,UAAU,CAACC,GAAG,CAAC,CAAC;EAC3C;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAACvC,YAAY,IAAI,CAAC,IAAI,CAACqC,UAAU,CAAC,CAAC,EAAE;MAC5C,OAAO,IAAI;IACb;IAEA,MAAMd,IAAI,GAAG,IAAI,CAACvB,YAAY,CAACwC,WAAW,CAACF,GAAG,CAAC,CAAC;IAChD,MAAMpB,OAAO,GAAGK,IAAI,CAACJ,eAAe,CAAC,CAAC;IAEtC,OAAO;MACLK,EAAE,EAAEN,OAAO,CAACO,KAAK,CAAC,CAAC;MACnBC,IAAI,EAAER,OAAO,CAACS,OAAO,CAAC,CAAC;MACvBC,KAAK,EAAEV,OAAO,CAACW,QAAQ,CAAC,CAAC;MACzBC,QAAQ,EAAEZ,OAAO,CAACa,WAAW,CAAC;IAChC,CAAC;EACH;AACF;;AAEA;AACA,MAAMU,iBAAiB,GAAG,IAAI5C,iBAAiB,CAAC,CAAC;AACjD,eAAe4C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}