{"ast": null, "code": "import { gapi, gapiComplete } from './gapiScript';\n\n/**\n * Function to load gapi auth2 from a gapi that you provied\n * Check full docs here: https://developers.google.com/identity/sign-in/web/reference#auth_setup\n * @param {Object} gapiScript gapi script object\n * @param {string} clientId Your google clientID string\n * @param {Array.<string[]>} scopes The scopes to request, as a space-delimited string. Optional if fetch_basic_profile is not set to false. Check possible scopes on google docs: https://developers.google.com/identity/protocols/oauth2/scopes\n */\nconst loadAuth2 = async function (gapiScript, clientId, scopes) {\n  return new Promise(resolve => {\n    gapiScript.load('auth2', () => {\n      resolve(gapiScript.auth2.init({\n        client_id: clientId,\n        scope: scopes\n      }));\n    });\n  });\n};\n\n/**\n * Function to init gapi auth2 with props\n * @param {Object} gapiScript gapi script object\n * @param {*} props Possible props to init gapi auth2, check the options on google docs: https://developers.google.com/identity/sign-in/web/reference#gapiauth2clientconfig\n */\nconst loadAuth2WithProps = async function (gapiScript, props) {\n  return new Promise(resolve => {\n    gapiScript.load('auth2', () => {\n      resolve(gapiScript.auth2.init(props));\n    });\n  });\n};\n\n/**\n *\n * @param {Object} gapiScript gapi script object\n * @param {string} clientId Your google clientID string\n * @param {Array.<string[]>} scopes The scopes to request, as a space-delimited string. Optional if fetch_basic_profile is not set to false. Check possible scopes on google docs: https://developers.google.com/identity/protocols/oauth2/scopes\n */\nconst loadClientAuth2 = async function (gapiScript, clientId, scopes) {\n  return new Promise(resolve => {\n    gapiScript.load('client', () => {\n      resolve(gapiScript.client.init({\n        client_id: clientId,\n        scope: scopes\n      }));\n    });\n    gapiScript.load('auth2', () => {\n      resolve(gapiScript.client.init({\n        client_id: clientId,\n        scope: scopes\n      }));\n    });\n  });\n};\n\n/**\n * A very special function to load the gapi inside the DOM, directly.\n * So it'll load the real and most recent gapi-scrip from google and attach to DOM:\n * let gapi = loadGapiInsideDOM();\n * Now you can use it anywhere.\n */\nconst loadGapiInsideDOM = async function () {\n  return new Promise(resolve => {\n    const element = document.getElementsByTagName('script')[0];\n    const js = document.createElement('script');\n    js.id = 'google-platform';\n    js.src = '//apis.google.com/js/platform.js';\n    js.async = true;\n    js.defer = true;\n    element.parentNode.insertBefore(js, element);\n    js.onload = async () => {\n      resolve(window.gapi);\n    };\n  });\n};\nexport { gapi, gapiComplete, loadAuth2, loadAuth2WithProps, loadClientAuth2, loadGapiInsideDOM };", "map": {"version": 3, "names": ["<PERSON>i", "gapiComplete", "loadAuth2", "gapiScript", "clientId", "scopes", "Promise", "resolve", "load", "auth2", "init", "client_id", "scope", "loadAuth2WithProps", "props", "loadClientAuth2", "client", "loadGapiInsideDOM", "element", "document", "getElementsByTagName", "js", "createElement", "id", "src", "async", "defer", "parentNode", "insertBefore", "onload", "window"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/node_modules/gapi-script/index.js"], "sourcesContent": ["import { gapi, gapiComplete } from './gapiScript';\n\n/**\n * Function to load gapi auth2 from a gapi that you provied\n * Check full docs here: https://developers.google.com/identity/sign-in/web/reference#auth_setup\n * @param {Object} gapiScript gapi script object\n * @param {string} clientId Your google clientID string\n * @param {Array.<string[]>} scopes The scopes to request, as a space-delimited string. Optional if fetch_basic_profile is not set to false. Check possible scopes on google docs: https://developers.google.com/identity/protocols/oauth2/scopes\n */\nconst loadAuth2 = async function (gapiScript, clientId, scopes) {\n  return new Promise(resolve => {\n    gapiScript.load('auth2', () => {\n      resolve(gapiScript.auth2.init({\n        client_id: clientId,\n        scope: scopes\n      }));\n    });\n  });\n}\n\n/**\n * Function to init gapi auth2 with props\n * @param {Object} gapiScript gapi script object\n * @param {*} props Possible props to init gapi auth2, check the options on google docs: https://developers.google.com/identity/sign-in/web/reference#gapiauth2clientconfig\n */\nconst loadAuth2WithProps = async function (gapiScript, props) {\n  return new Promise(resolve => {\n    gapiScript.load('auth2', () => {\n      resolve(gapiScript.auth2.init(props));\n    });\n  });\n}\n\n/**\n *\n * @param {Object} gapiScript gapi script object\n * @param {string} clientId Your google clientID string\n * @param {Array.<string[]>} scopes The scopes to request, as a space-delimited string. Optional if fetch_basic_profile is not set to false. Check possible scopes on google docs: https://developers.google.com/identity/protocols/oauth2/scopes\n */\nconst loadClientAuth2 = async function (gapiScript, clientId, scopes) {\n  return new Promise(resolve => {\n      gapiScript.load('client', () => {\n          resolve(gapiScript.client.init({\n              client_id: clientId,\n              scope: scopes\n          }));\n      });\n      gapiScript.load('auth2', () => {\n          resolve(gapiScript.client.init({\n              client_id: clientId,\n              scope: scopes\n          }));\n      });\n  });\n}\n\n/**\n * A very special function to load the gapi inside the DOM, directly.\n * So it'll load the real and most recent gapi-scrip from google and attach to DOM:\n * let gapi = loadGapiInsideDOM();\n * Now you can use it anywhere.\n */\nconst loadGapiInsideDOM = async function () {\n  return new Promise(resolve => {\n    const element = document.getElementsByTagName('script')[0];\n    const js = document.createElement('script');\n    js.id = 'google-platform';\n    js.src = '//apis.google.com/js/platform.js';\n    js.async = true;\n    js.defer = true;\n    element.parentNode.insertBefore(js, element);\n    js.onload = async () => {\n      resolve(window.gapi);\n    }\n  });\n}\n\nexport {\n  gapi,\n  gapiComplete,\n  loadAuth2,\n  loadAuth2WithProps,\n  loadClientAuth2,\n  loadGapiInsideDOM,\n};\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,YAAY,QAAQ,cAAc;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,eAAAA,CAAgBC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC9D,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC5BJ,UAAU,CAACK,IAAI,CAAC,OAAO,EAAE,MAAM;MAC7BD,OAAO,CAACJ,UAAU,CAACM,KAAK,CAACC,IAAI,CAAC;QAC5BC,SAAS,EAAEP,QAAQ;QACnBQ,KAAK,EAAEP;MACT,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMQ,kBAAkB,GAAG,eAAAA,CAAgBV,UAAU,EAAEW,KAAK,EAAE;EAC5D,OAAO,IAAIR,OAAO,CAACC,OAAO,IAAI;IAC5BJ,UAAU,CAACK,IAAI,CAAC,OAAO,EAAE,MAAM;MAC7BD,OAAO,CAACJ,UAAU,CAACM,KAAK,CAACC,IAAI,CAACI,KAAK,CAAC,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,eAAAA,CAAgBZ,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACpE,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC1BJ,UAAU,CAACK,IAAI,CAAC,QAAQ,EAAE,MAAM;MAC5BD,OAAO,CAACJ,UAAU,CAACa,MAAM,CAACN,IAAI,CAAC;QAC3BC,SAAS,EAAEP,QAAQ;QACnBQ,KAAK,EAAEP;MACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACFF,UAAU,CAACK,IAAI,CAAC,OAAO,EAAE,MAAM;MAC3BD,OAAO,CAACJ,UAAU,CAACa,MAAM,CAACN,IAAI,CAAC;QAC3BC,SAAS,EAAEP,QAAQ;QACnBQ,KAAK,EAAEP;MACX,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,iBAAiB,GAAG,eAAAA,CAAA,EAAkB;EAC1C,OAAO,IAAIX,OAAO,CAACC,OAAO,IAAI;IAC5B,MAAMW,OAAO,GAAGC,QAAQ,CAACC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAMC,EAAE,GAAGF,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;IAC3CD,EAAE,CAACE,EAAE,GAAG,iBAAiB;IACzBF,EAAE,CAACG,GAAG,GAAG,kCAAkC;IAC3CH,EAAE,CAACI,KAAK,GAAG,IAAI;IACfJ,EAAE,CAACK,KAAK,GAAG,IAAI;IACfR,OAAO,CAACS,UAAU,CAACC,YAAY,CAACP,EAAE,EAAEH,OAAO,CAAC;IAC5CG,EAAE,CAACQ,MAAM,GAAG,YAAY;MACtBtB,OAAO,CAACuB,MAAM,CAAC9B,IAAI,CAAC;IACtB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAED,SACEA,IAAI,EACJC,YAAY,EACZC,SAAS,EACTW,kBAAkB,EAClBE,eAAe,EACfE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}