import { useState } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Container,
  Paper,
  Typography,
  Button,
  Link,
  Box,
  CircularProgress,
  Snackbar,
  Alert,
  Grid,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { PersonAddOutlined as PersonAddIcon } from '@mui/icons-material';
import FormControlWrapper from '../components/FormControlWrapper';
import PasswordField from '../components/PasswordField';
import GoogleOAuthButton from '../components/GoogleOAuthButton';
import OrDivider from '../components/OrDivider';
import authService from '../api/authService';

const validationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Confirm password is required')
});

const defaultValues = {
  name: '',
  email: '',
  password: '',
  confirmPassword: ''
};

const Signup = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues,
    mode: 'onChange',
    resolver: yupResolver(validationSchema)
  });

  const onSubmit = async (values) => {
    setLoading(true);

    const handleSuccess = (data) => {
      console.log('Signup successful:', data);
      navigate('/login');
    };

    const handleError = (errorMessage) => {
      setError(errorMessage);
      setShowError(true);
    };

    try {
      await authService.signup({
        name: values.name,
        email: values.email,
        password: values.password
      }, handleError, handleSuccess);
    } catch (err) {
      // Error already handled by authService
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (googleData) => {
    setGoogleLoading(true);

    const handleSuccess = (data) => {
      console.log('Google signup successful:', data);
      navigate('/profile');
    };

    const handleError = (errorMessage) => {
      setError(errorMessage);
      setShowError(true);
    };

    try {
      await authService.googleLogin(googleData, handleError, handleSuccess);
    } catch (err) {
      // Error already handled by authService
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleGoogleError = (errorMessage) => {
    setError(errorMessage);
    setShowError(true);
  };

  return (
    <Container
      component="main"
      maxWidth="xs"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        py: { xs: 2, sm: 4 }
      }}
    >
      <Paper
        elevation={isMobile ? 0 : 3}
        sx={{
          p: { xs: 3, sm: 4 },
          width: '100%',
          backgroundColor: isMobile ? 'transparent' : 'background.paper',
          boxShadow: isMobile ? 'none' : undefined
        }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box
            sx={{
              mx: 'auto',
              mb: 2,
              width: 48,
              height: 48,
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <PersonAddIcon sx={{ color: 'white', fontSize: 24 }} />
          </Box>
          <Typography
            component="h1"
            variant="h4"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: 1
            }}
          >
            Create account
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              fontSize: '14px'
            }}
          >
            Sign up to get started with your account
          </Typography>
        </Box>

        {/* Google OAuth Button */}
        <Box sx={{ mb: 2 }}>
          <GoogleOAuthButton
            onSuccess={handleGoogleSuccess}
            onError={handleGoogleError}
            disabled={loading || googleLoading}
            text="Continue with Google"
          />
        </Box>

        {/* Divider */}
        <OrDivider />

        {/* Registration Form */}
        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControlWrapper
                name="name"
                control={control}
                rules={{ required: 'Name is required' }}
                label="Full Name"
                placeholder="Enter your full name"
                error={errors.name}
                helperText={errors.name?.message}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlWrapper
                name="email"
                control={control}
                rules={{ required: 'Email is required' }}
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                error={errors.email}
                helperText={errors.email?.message}
              />
            </Grid>
            <Grid item xs={12}>
              <PasswordField
                name="password"
                control={control}
                rules={{ required: 'Password is required' }}
                label="Password"
                placeholder="Enter your password"
                error={errors.password}
                helperText={errors.password?.message}
              />
            </Grid>
            <Grid item xs={12}>
              <PasswordField
                name="confirmPassword"
                control={control}
                rules={{ required: 'Confirm password is required' }}
                label="Confirm Password"
                placeholder="Confirm your password"
                error={errors.confirmPassword}
                helperText={errors.confirmPassword?.message}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{
                  mt: 1,
                  mb: 2,
                  height: 48,
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '16px'
                }}
                disabled={loading || googleLoading}
              >
                {loading ? (
                  <>
                    <CircularProgress
                      size={20}
                      sx={{ color: 'common.white', mr: 1 }}
                    />
                    Creating account...
                  </>
                ) : (
                  'Create account'
                )}
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Footer Links */}
        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Already have an account?{' '}
            <Link
              component={RouterLink}
              to="/login"
              sx={{
                color: 'primary.main',
                textDecoration: 'none',
                fontWeight: 600,
                '&:hover': {
                  textDecoration: 'underline'
                }
              }}
            >
              Sign in
            </Link>
          </Typography>
        </Box>
      </Paper>

      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity="error"
          onClose={() => setShowError(false)}
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Signup; 