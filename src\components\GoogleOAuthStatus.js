import { useState, useEffect } from 'react';
import { 
  <PERSON>ert, 
  AlertTitle, 
  Box, 
  Typography, 
  Link,
  Chip
} from '@mui/material';
import { 
  CheckCircle as CheckIcon, 
  Warning as WarningIcon,
  Info as InfoIcon 
} from '@mui/icons-material';
import { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';

const GoogleOAuthStatus = () => {
  const [status, setStatus] = useState('checking');
  const [message, setMessage] = useState('');

  useEffect(() => {
    checkGoogleOAuthSetup();
  }, []);

  const checkGoogleOAuthSetup = () => {
    const clientId = GOOGLE_OAUTH_CONFIG.clientId;
    
    if (!clientId || clientId === 'YOUR_GOOGLE_CLIENT_ID_HERE') {
      setStatus('not-configured');
      setMessage('Google OAuth is not configured. Please follow the setup guide.');
    } else if (clientId.includes('your_google_client_id_here')) {
      setStatus('not-configured');
      setMessage('Please replace the placeholder with your actual Google Client ID.');
    } else {
      setStatus('configured');
      setMessage('Google OAuth is properly configured and ready to use.');
    }
  };

  const getAlertProps = () => {
    switch (status) {
      case 'configured':
        return {
          severity: 'success',
          icon: <CheckIcon />,
          title: 'Google OAuth Ready'
        };
      case 'not-configured':
        return {
          severity: 'warning',
          icon: <WarningIcon />,
          title: 'Setup Required'
        };
      default:
        return {
          severity: 'info',
          icon: <InfoIcon />,
          title: 'Checking Configuration'
        };
    }
  };

  const alertProps = getAlertProps();

  return (
    <Box sx={{ mb: 2 }}>
      <Alert 
        severity={alertProps.severity} 
        icon={alertProps.icon}
        sx={{ 
          borderRadius: 2,
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
      >
        <AlertTitle sx={{ fontWeight: 600 }}>
          {alertProps.title}
        </AlertTitle>
        
        <Typography variant="body2" sx={{ mb: 1 }}>
          {message}
        </Typography>

        {status === 'configured' && (
          <Box sx={{ mt: 1 }}>
            <Chip 
              label="OAuth Ready" 
              color="success" 
              size="small" 
              sx={{ mr: 1 }}
            />
            <Chip 
              label="Responsive Design" 
              color="primary" 
              size="small" 
              sx={{ mr: 1 }}
            />
            <Chip 
              label="Mobile Optimized" 
              color="secondary" 
              size="small" 
            />
          </Box>
        )}

        {status === 'not-configured' && (
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              To enable Google OAuth:
            </Typography>
            <Typography variant="body2" component="div">
              1. Follow the setup guide in{' '}
              <Link 
                href="/GOOGLE_OAUTH_SETUP.md" 
                target="_blank"
                sx={{ fontWeight: 600 }}
              >
                GOOGLE_OAUTH_SETUP.md
              </Link>
              <br />
              2. Update your <code>.env</code> file with the Google Client ID
              <br />
              3. Restart the development server
            </Typography>
          </Box>
        )}
      </Alert>
    </Box>
  );
};

export default GoogleOAuthStatus;
