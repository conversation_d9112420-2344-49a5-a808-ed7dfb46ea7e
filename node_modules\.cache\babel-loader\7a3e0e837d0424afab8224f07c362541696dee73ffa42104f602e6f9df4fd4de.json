{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\pages\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as Yup from 'yup';\nimport { Container, Paper, Typography, Button, Link, Box, CircularProgress, Snackbar, Alert, Grid, useTheme, useMediaQuery } from '@mui/material';\nimport { PersonAddOutlined as PersonAddIcon } from '@mui/icons-material';\nimport FormControlWrapper from '../components/FormControlWrapper';\nimport PasswordField from '../components/PasswordField';\nimport GoogleOAuthButton from '../components/GoogleOAuthButton';\nimport OrDivider from '../components/OrDivider';\nimport authService from '../api/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst validationSchema = Yup.object({\n  name: Yup.string().required('Name is required'),\n  email: Yup.string().email('Invalid email address').required('Email is required'),\n  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),\n  confirmPassword: Yup.string().oneOf([Yup.ref('password'), null], 'Passwords must match').required('Confirm password is required')\n});\nconst defaultValues = {\n  name: '',\n  email: '',\n  password: '',\n  confirmPassword: ''\n};\nconst Signup = () => {\n  _s();\n  var _errors$name, _errors$email, _errors$password, _errors$confirmPasswo;\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const [loading, setLoading] = useState(false);\n  const [googleLoading, setGoogleLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showError, setShowError] = useState(false);\n  const {\n    control,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm({\n    defaultValues,\n    mode: 'onChange',\n    resolver: yupResolver(validationSchema)\n  });\n  const onSubmit = async values => {\n    setLoading(true);\n    const handleSuccess = data => {\n      console.log('Signup successful:', data);\n      navigate('/login');\n    };\n    const handleError = errorMessage => {\n      setError(errorMessage);\n      setShowError(true);\n    };\n    try {\n      await authService.signup({\n        name: values.name,\n        email: values.email,\n        password: values.password\n      }, handleError, handleSuccess);\n    } catch (err) {\n      // Error already handled by authService\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGoogleSuccess = async googleData => {\n    setGoogleLoading(true);\n    const handleSuccess = data => {\n      console.log('Google signup successful:', data);\n      navigate('/profile');\n    };\n    const handleError = errorMessage => {\n      setError(errorMessage);\n      setShowError(true);\n    };\n    try {\n      await authService.googleLogin(googleData, handleError, handleSuccess);\n    } catch (err) {\n      // Error already handled by authService\n    } finally {\n      setGoogleLoading(false);\n    }\n  };\n  const handleGoogleError = errorMessage => {\n    setError(errorMessage);\n    setShowError(true);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"xs\",\n    sx: {\n      minHeight: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      py: {\n        xs: 2,\n        sm: 4\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: isMobile ? 0 : 3,\n      sx: {\n        p: {\n          xs: 3,\n          sm: 4\n        },\n        width: '100%',\n        backgroundColor: isMobile ? 'transparent' : 'background.paper',\n        boxShadow: isMobile ? 'none' : undefined\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mx: 'auto',\n            mb: 2,\n            width: 48,\n            height: 48,\n            borderRadius: '50%',\n            backgroundColor: 'primary.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(PersonAddIcon, {\n            sx: {\n              color: 'white',\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          component: \"h1\",\n          variant: \"h4\",\n          sx: {\n            fontWeight: 600,\n            color: 'text.primary',\n            mb: 1\n          },\n          children: \"Create account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary',\n            fontSize: '14px'\n          },\n          children: \"Sign up to get started with your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(GoogleOAuthButton, {\n          onSuccess: handleGoogleSuccess,\n          onError: handleGoogleError,\n          disabled: loading || googleLoading,\n          text: \"Continue with Google\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(OrDivider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"form\",\n        onSubmit: handleSubmit(onSubmit),\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControlWrapper, {\n              name: \"name\",\n              control: control,\n              rules: {\n                required: 'Name is required'\n              },\n              label: \"Full Name\",\n              placeholder: \"Enter your full name\",\n              error: errors.name,\n              helperText: (_errors$name = errors.name) === null || _errors$name === void 0 ? void 0 : _errors$name.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControlWrapper, {\n              name: \"email\",\n              control: control,\n              rules: {\n                required: 'Email is required'\n              },\n              label: \"Email Address\",\n              type: \"email\",\n              placeholder: \"Enter your email\",\n              error: errors.email,\n              helperText: (_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(PasswordField, {\n              name: \"password\",\n              control: control,\n              rules: {\n                required: 'Password is required'\n              },\n              label: \"Password\",\n              placeholder: \"Enter your password\",\n              error: errors.password,\n              helperText: (_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(PasswordField, {\n              name: \"confirmPassword\",\n              control: control,\n              rules: {\n                required: 'Confirm password is required'\n              },\n              label: \"Confirm Password\",\n              placeholder: \"Confirm your password\",\n              error: errors.confirmPassword,\n              helperText: (_errors$confirmPasswo = errors.confirmPassword) === null || _errors$confirmPasswo === void 0 ? void 0 : _errors$confirmPasswo.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              fullWidth: true,\n              variant: \"contained\",\n              size: \"large\",\n              sx: {\n                mt: 1,\n                mb: 2,\n                height: 48,\n                textTransform: 'none',\n                fontWeight: 600,\n                fontSize: '16px'\n              },\n              disabled: loading || googleLoading,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20,\n                  sx: {\n                    color: 'common.white',\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), \"Creating account...\"]\n              }, void 0, true) : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          mt: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary'\n          },\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            component: RouterLink,\n            to: \"/login\",\n            sx: {\n              color: 'primary.main',\n              textDecoration: 'none',\n              fontWeight: 600,\n              '&:hover': {\n                textDecoration: 'underline'\n              }\n            },\n            children: \"Sign in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: () => setShowError(false),\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        onClose: () => setShowError(false),\n        sx: {\n          width: '100%'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(Signup, \"fBRWYVwyl22QP9S1Eth3CTyZ7EY=\", false, function () {\n  return [useNavigate, useTheme, useMediaQuery, useForm];\n});\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["useState", "useNavigate", "Link", "RouterLink", "useForm", "yupResolver", "<PERSON><PERSON>", "Container", "Paper", "Typography", "<PERSON><PERSON>", "Box", "CircularProgress", "Snackbar", "<PERSON><PERSON>", "Grid", "useTheme", "useMediaQuery", "PersonAddOutlined", "PersonAddIcon", "FormControlWrapper", "PasswordField", "GoogleOAuthButton", "OrDivider", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "validationSchema", "object", "name", "string", "required", "email", "password", "min", "confirmPassword", "oneOf", "ref", "defaultValues", "Signup", "_s", "_errors$name", "_errors$email", "_errors$password", "_errors$confirmPasswo", "navigate", "theme", "isMobile", "breakpoints", "down", "loading", "setLoading", "googleLoading", "setGoogleLoading", "error", "setError", "showError", "setShowError", "control", "handleSubmit", "formState", "errors", "mode", "resolver", "onSubmit", "values", "handleSuccess", "data", "console", "log", "handleError", "errorMessage", "signup", "err", "handleGoogleSuccess", "googleData", "googleLogin", "handleGoogleError", "component", "max<PERSON><PERSON><PERSON>", "sx", "minHeight", "display", "alignItems", "py", "xs", "sm", "children", "elevation", "p", "width", "backgroundColor", "boxShadow", "undefined", "textAlign", "mb", "mx", "height", "borderRadius", "justifyContent", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "onSuccess", "onError", "disabled", "text", "container", "spacing", "item", "rules", "label", "placeholder", "helperText", "message", "type", "fullWidth", "size", "mt", "textTransform", "mr", "to", "textDecoration", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/pages/Signup.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useNavigate, Link as RouterLink } from 'react-router-dom';\r\nimport { useForm } from 'react-hook-form';\r\nimport { yupResolver } from '@hookform/resolvers/yup';\r\nimport * as Yup from 'yup';\r\nimport {\r\n  Container,\r\n  Paper,\r\n  Typography,\r\n  Button,\r\n  Link,\r\n  Box,\r\n  CircularProgress,\r\n  Snackbar,\r\n  Alert,\r\n  Grid,\r\n  useTheme,\r\n  useMediaQuery,\r\n} from '@mui/material';\r\nimport { PersonAddOutlined as PersonAddIcon } from '@mui/icons-material';\r\nimport FormControlWrapper from '../components/FormControlWrapper';\r\nimport PasswordField from '../components/PasswordField';\r\nimport GoogleOAuthButton from '../components/GoogleOAuthButton';\r\nimport OrDivider from '../components/OrDivider';\r\nimport authService from '../api/authService';\r\n\r\nconst validationSchema = Yup.object({\r\n  name: Yup.string()\r\n    .required('Name is required'),\r\n  email: Yup.string()\r\n    .email('Invalid email address')\r\n    .required('Email is required'),\r\n  password: Yup.string()\r\n    .min(6, 'Password must be at least 6 characters')\r\n    .required('Password is required'),\r\n  confirmPassword: Yup.string()\r\n    .oneOf([Yup.ref('password'), null], 'Passwords must match')\r\n    .required('Confirm password is required')\r\n});\r\n\r\nconst defaultValues = {\r\n  name: '',\r\n  email: '',\r\n  password: '',\r\n  confirmPassword: ''\r\n};\r\n\r\nconst Signup = () => {\r\n  const navigate = useNavigate();\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\r\n  const [loading, setLoading] = useState(false);\r\n  const [googleLoading, setGoogleLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [showError, setShowError] = useState(false);\r\n\r\n  const {\r\n    control,\r\n    handleSubmit,\r\n    formState: { errors }\r\n  } = useForm({\r\n    defaultValues,\r\n    mode: 'onChange',\r\n    resolver: yupResolver(validationSchema)\r\n  });\r\n\r\n  const onSubmit = async (values) => {\r\n    setLoading(true);\r\n\r\n    const handleSuccess = (data) => {\r\n      console.log('Signup successful:', data);\r\n      navigate('/login');\r\n    };\r\n\r\n    const handleError = (errorMessage) => {\r\n      setError(errorMessage);\r\n      setShowError(true);\r\n    };\r\n\r\n    try {\r\n      await authService.signup({\r\n        name: values.name,\r\n        email: values.email,\r\n        password: values.password\r\n      }, handleError, handleSuccess);\r\n    } catch (err) {\r\n      // Error already handled by authService\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleGoogleSuccess = async (googleData) => {\r\n    setGoogleLoading(true);\r\n\r\n    const handleSuccess = (data) => {\r\n      console.log('Google signup successful:', data);\r\n      navigate('/profile');\r\n    };\r\n\r\n    const handleError = (errorMessage) => {\r\n      setError(errorMessage);\r\n      setShowError(true);\r\n    };\r\n\r\n    try {\r\n      await authService.googleLogin(googleData, handleError, handleSuccess);\r\n    } catch (err) {\r\n      // Error already handled by authService\r\n    } finally {\r\n      setGoogleLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleGoogleError = (errorMessage) => {\r\n    setError(errorMessage);\r\n    setShowError(true);\r\n  };\r\n\r\n  return (\r\n    <Container\r\n      component=\"main\"\r\n      maxWidth=\"xs\"\r\n      sx={{\r\n        minHeight: '100vh',\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        py: { xs: 2, sm: 4 }\r\n      }}\r\n    >\r\n      <Paper\r\n        elevation={isMobile ? 0 : 3}\r\n        sx={{\r\n          p: { xs: 3, sm: 4 },\r\n          width: '100%',\r\n          backgroundColor: isMobile ? 'transparent' : 'background.paper',\r\n          boxShadow: isMobile ? 'none' : undefined\r\n        }}\r\n      >\r\n        {/* Header */}\r\n        <Box sx={{ textAlign: 'center', mb: 3 }}>\r\n          <Box\r\n            sx={{\r\n              mx: 'auto',\r\n              mb: 2,\r\n              width: 48,\r\n              height: 48,\r\n              borderRadius: '50%',\r\n              backgroundColor: 'primary.main',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center'\r\n            }}\r\n          >\r\n            <PersonAddIcon sx={{ color: 'white', fontSize: 24 }} />\r\n          </Box>\r\n          <Typography\r\n            component=\"h1\"\r\n            variant=\"h4\"\r\n            sx={{\r\n              fontWeight: 600,\r\n              color: 'text.primary',\r\n              mb: 1\r\n            }}\r\n          >\r\n            Create account\r\n          </Typography>\r\n          <Typography\r\n            variant=\"body2\"\r\n            sx={{\r\n              color: 'text.secondary',\r\n              fontSize: '14px'\r\n            }}\r\n          >\r\n            Sign up to get started with your account\r\n          </Typography>\r\n        </Box>\r\n\r\n        {/* Google OAuth Button */}\r\n        <Box sx={{ mb: 2 }}>\r\n          <GoogleOAuthButton\r\n            onSuccess={handleGoogleSuccess}\r\n            onError={handleGoogleError}\r\n            disabled={loading || googleLoading}\r\n            text=\"Continue with Google\"\r\n          />\r\n        </Box>\r\n\r\n        {/* Divider */}\r\n        <OrDivider />\r\n\r\n        {/* Registration Form */}\r\n        <Box component=\"form\" onSubmit={handleSubmit(onSubmit)}>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12}>\r\n              <FormControlWrapper\r\n                name=\"name\"\r\n                control={control}\r\n                rules={{ required: 'Name is required' }}\r\n                label=\"Full Name\"\r\n                placeholder=\"Enter your full name\"\r\n                error={errors.name}\r\n                helperText={errors.name?.message}\r\n              />\r\n            </Grid>\r\n            <Grid item xs={12}>\r\n              <FormControlWrapper\r\n                name=\"email\"\r\n                control={control}\r\n                rules={{ required: 'Email is required' }}\r\n                label=\"Email Address\"\r\n                type=\"email\"\r\n                placeholder=\"Enter your email\"\r\n                error={errors.email}\r\n                helperText={errors.email?.message}\r\n              />\r\n            </Grid>\r\n            <Grid item xs={12}>\r\n              <PasswordField\r\n                name=\"password\"\r\n                control={control}\r\n                rules={{ required: 'Password is required' }}\r\n                label=\"Password\"\r\n                placeholder=\"Enter your password\"\r\n                error={errors.password}\r\n                helperText={errors.password?.message}\r\n              />\r\n            </Grid>\r\n            <Grid item xs={12}>\r\n              <PasswordField\r\n                name=\"confirmPassword\"\r\n                control={control}\r\n                rules={{ required: 'Confirm password is required' }}\r\n                label=\"Confirm Password\"\r\n                placeholder=\"Confirm your password\"\r\n                error={errors.confirmPassword}\r\n                helperText={errors.confirmPassword?.message}\r\n              />\r\n            </Grid>\r\n            <Grid item xs={12}>\r\n              <Button\r\n                type=\"submit\"\r\n                fullWidth\r\n                variant=\"contained\"\r\n                size=\"large\"\r\n                sx={{\r\n                  mt: 1,\r\n                  mb: 2,\r\n                  height: 48,\r\n                  textTransform: 'none',\r\n                  fontWeight: 600,\r\n                  fontSize: '16px'\r\n                }}\r\n                disabled={loading || googleLoading}\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <CircularProgress\r\n                      size={20}\r\n                      sx={{ color: 'common.white', mr: 1 }}\r\n                    />\r\n                    Creating account...\r\n                  </>\r\n                ) : (\r\n                  'Create account'\r\n                )}\r\n              </Button>\r\n            </Grid>\r\n          </Grid>\r\n        </Box>\r\n\r\n        {/* Footer Links */}\r\n        <Box sx={{ textAlign: 'center', mt: 3 }}>\r\n          <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\r\n            Already have an account?{' '}\r\n            <Link\r\n              component={RouterLink}\r\n              to=\"/login\"\r\n              sx={{\r\n                color: 'primary.main',\r\n                textDecoration: 'none',\r\n                fontWeight: 600,\r\n                '&:hover': {\r\n                  textDecoration: 'underline'\r\n                }\r\n              }}\r\n            >\r\n              Sign in\r\n            </Link>\r\n          </Typography>\r\n        </Box>\r\n      </Paper>\r\n\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={() => setShowError(false)}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          severity=\"error\"\r\n          onClose={() => setShowError(false)}\r\n          sx={{ width: '100%' }}\r\n        >\r\n          {error}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Signup; "], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,WAAW,EAAEC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AAClE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNR,IAAI,EACJS,GAAG,EACHC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,SAASC,iBAAiB,IAAIC,aAAa,QAAQ,qBAAqB;AACxE,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,WAAW,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,gBAAgB,GAAGvB,GAAG,CAACwB,MAAM,CAAC;EAClCC,IAAI,EAAEzB,GAAG,CAAC0B,MAAM,CAAC,CAAC,CACfC,QAAQ,CAAC,kBAAkB,CAAC;EAC/BC,KAAK,EAAE5B,GAAG,CAAC0B,MAAM,CAAC,CAAC,CAChBE,KAAK,CAAC,uBAAuB,CAAC,CAC9BD,QAAQ,CAAC,mBAAmB,CAAC;EAChCE,QAAQ,EAAE7B,GAAG,CAAC0B,MAAM,CAAC,CAAC,CACnBI,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDH,QAAQ,CAAC,sBAAsB,CAAC;EACnCI,eAAe,EAAE/B,GAAG,CAAC0B,MAAM,CAAC,CAAC,CAC1BM,KAAK,CAAC,CAAChC,GAAG,CAACiC,GAAG,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC1DN,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,MAAMO,aAAa,GAAG;EACpBT,IAAI,EAAE,EAAE;EACRG,KAAK,EAAE,EAAE;EACTC,QAAQ,EAAE,EAAE;EACZE,eAAe,EAAE;AACnB,CAAC;AAED,MAAMI,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACnB,MAAMC,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM+C,KAAK,GAAGhC,QAAQ,CAAC,CAAC;EACxB,MAAMiC,QAAQ,GAAGhC,aAAa,CAAC+B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM;IACJ4D,OAAO;IACPC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAG3D,OAAO,CAAC;IACVoC,aAAa;IACbwB,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAE5D,WAAW,CAACwB,gBAAgB;EACxC,CAAC,CAAC;EAEF,MAAMqC,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCd,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMe,aAAa,GAAIC,IAAI,IAAK;MAC9BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,IAAI,CAAC;MACvCtB,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC;IAED,MAAMyB,WAAW,GAAIC,YAAY,IAAK;MACpChB,QAAQ,CAACgB,YAAY,CAAC;MACtBd,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC;IAED,IAAI;MACF,MAAMnC,WAAW,CAACkD,MAAM,CAAC;QACvB3C,IAAI,EAAEoC,MAAM,CAACpC,IAAI;QACjBG,KAAK,EAAEiC,MAAM,CAACjC,KAAK;QACnBC,QAAQ,EAAEgC,MAAM,CAAChC;MACnB,CAAC,EAAEqC,WAAW,EAAEJ,aAAa,CAAC;IAChC,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZ;IAAA,CACD,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,mBAAmB,GAAG,MAAOC,UAAU,IAAK;IAChDtB,gBAAgB,CAAC,IAAI,CAAC;IAEtB,MAAMa,aAAa,GAAIC,IAAI,IAAK;MAC9BC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,IAAI,CAAC;MAC9CtB,QAAQ,CAAC,UAAU,CAAC;IACtB,CAAC;IAED,MAAMyB,WAAW,GAAIC,YAAY,IAAK;MACpChB,QAAQ,CAACgB,YAAY,CAAC;MACtBd,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC;IAED,IAAI;MACF,MAAMnC,WAAW,CAACsD,WAAW,CAACD,UAAU,EAAEL,WAAW,EAAEJ,aAAa,CAAC;IACvE,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZ;IAAA,CACD,SAAS;MACRpB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMwB,iBAAiB,GAAIN,YAAY,IAAK;IAC1ChB,QAAQ,CAACgB,YAAY,CAAC;IACtBd,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,oBACEjC,OAAA,CAACnB,SAAS;IACRyE,SAAS,EAAC,MAAM;IAChBC,QAAQ,EAAC,IAAI;IACbC,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IACrB,CAAE;IAAAC,QAAA,gBAEF/D,OAAA,CAAClB,KAAK;MACJkF,SAAS,EAAEzC,QAAQ,GAAG,CAAC,GAAG,CAAE;MAC5BiC,EAAE,EAAE;QACFS,CAAC,EAAE;UAAEJ,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACnBI,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE5C,QAAQ,GAAG,aAAa,GAAG,kBAAkB;QAC9D6C,SAAS,EAAE7C,QAAQ,GAAG,MAAM,GAAG8C;MACjC,CAAE;MAAAN,QAAA,gBAGF/D,OAAA,CAACf,GAAG;QAACuE,EAAE,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACtC/D,OAAA,CAACf,GAAG;UACFuE,EAAE,EAAE;YACFgB,EAAE,EAAE,MAAM;YACVD,EAAE,EAAE,CAAC;YACLL,KAAK,EAAE,EAAE;YACTO,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBP,eAAe,EAAE,cAAc;YAC/BT,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBgB,cAAc,EAAE;UAClB,CAAE;UAAAZ,QAAA,eAEF/D,OAAA,CAACP,aAAa;YAAC+D,EAAE,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAG;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNjF,OAAA,CAACjB,UAAU;UACTuE,SAAS,EAAC,IAAI;UACd4B,OAAO,EAAC,IAAI;UACZ1B,EAAE,EAAE;YACF2B,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,cAAc;YACrBL,EAAE,EAAE;UACN,CAAE;UAAAR,QAAA,EACH;QAED;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACjB,UAAU;UACTmG,OAAO,EAAC,OAAO;UACf1B,EAAE,EAAE;YACFoB,KAAK,EAAE,gBAAgB;YACvBC,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,EACH;QAED;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNjF,OAAA,CAACf,GAAG;QAACuE,EAAE,EAAE;UAAEe,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eACjB/D,OAAA,CAACJ,iBAAiB;UAChBwF,SAAS,EAAElC,mBAAoB;UAC/BmC,OAAO,EAAEhC,iBAAkB;UAC3BiC,QAAQ,EAAE5D,OAAO,IAAIE,aAAc;UACnC2D,IAAI,EAAC;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNjF,OAAA,CAACH,SAAS;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGbjF,OAAA,CAACf,GAAG;QAACqE,SAAS,EAAC,MAAM;QAACd,QAAQ,EAAEL,YAAY,CAACK,QAAQ,CAAE;QAAAuB,QAAA,eACrD/D,OAAA,CAACX,IAAI;UAACmG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzB/D,OAAA,CAACX,IAAI;YAACqG,IAAI;YAAC7B,EAAE,EAAE,EAAG;YAAAE,QAAA,eAChB/D,OAAA,CAACN,kBAAkB;cACjBW,IAAI,EAAC,MAAM;cACX6B,OAAO,EAAEA,OAAQ;cACjByD,KAAK,EAAE;gBAAEpF,QAAQ,EAAE;cAAmB,CAAE;cACxCqF,KAAK,EAAC,WAAW;cACjBC,WAAW,EAAC,sBAAsB;cAClC/D,KAAK,EAAEO,MAAM,CAAChC,IAAK;cACnByF,UAAU,GAAA7E,YAAA,GAAEoB,MAAM,CAAChC,IAAI,cAAAY,YAAA,uBAAXA,YAAA,CAAa8E;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACX,IAAI;YAACqG,IAAI;YAAC7B,EAAE,EAAE,EAAG;YAAAE,QAAA,eAChB/D,OAAA,CAACN,kBAAkB;cACjBW,IAAI,EAAC,OAAO;cACZ6B,OAAO,EAAEA,OAAQ;cACjByD,KAAK,EAAE;gBAAEpF,QAAQ,EAAE;cAAoB,CAAE;cACzCqF,KAAK,EAAC,eAAe;cACrBI,IAAI,EAAC,OAAO;cACZH,WAAW,EAAC,kBAAkB;cAC9B/D,KAAK,EAAEO,MAAM,CAAC7B,KAAM;cACpBsF,UAAU,GAAA5E,aAAA,GAAEmB,MAAM,CAAC7B,KAAK,cAAAU,aAAA,uBAAZA,aAAA,CAAc6E;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACX,IAAI;YAACqG,IAAI;YAAC7B,EAAE,EAAE,EAAG;YAAAE,QAAA,eAChB/D,OAAA,CAACL,aAAa;cACZU,IAAI,EAAC,UAAU;cACf6B,OAAO,EAAEA,OAAQ;cACjByD,KAAK,EAAE;gBAAEpF,QAAQ,EAAE;cAAuB,CAAE;cAC5CqF,KAAK,EAAC,UAAU;cAChBC,WAAW,EAAC,qBAAqB;cACjC/D,KAAK,EAAEO,MAAM,CAAC5B,QAAS;cACvBqF,UAAU,GAAA3E,gBAAA,GAAEkB,MAAM,CAAC5B,QAAQ,cAAAU,gBAAA,uBAAfA,gBAAA,CAAiB4E;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACX,IAAI;YAACqG,IAAI;YAAC7B,EAAE,EAAE,EAAG;YAAAE,QAAA,eAChB/D,OAAA,CAACL,aAAa;cACZU,IAAI,EAAC,iBAAiB;cACtB6B,OAAO,EAAEA,OAAQ;cACjByD,KAAK,EAAE;gBAAEpF,QAAQ,EAAE;cAA+B,CAAE;cACpDqF,KAAK,EAAC,kBAAkB;cACxBC,WAAW,EAAC,uBAAuB;cACnC/D,KAAK,EAAEO,MAAM,CAAC1B,eAAgB;cAC9BmF,UAAU,GAAA1E,qBAAA,GAAEiB,MAAM,CAAC1B,eAAe,cAAAS,qBAAA,uBAAtBA,qBAAA,CAAwB2E;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACX,IAAI;YAACqG,IAAI;YAAC7B,EAAE,EAAE,EAAG;YAAAE,QAAA,eAChB/D,OAAA,CAAChB,MAAM;cACLgH,IAAI,EAAC,QAAQ;cACbC,SAAS;cACTf,OAAO,EAAC,WAAW;cACnBgB,IAAI,EAAC,OAAO;cACZ1C,EAAE,EAAE;gBACF2C,EAAE,EAAE,CAAC;gBACL5B,EAAE,EAAE,CAAC;gBACLE,MAAM,EAAE,EAAE;gBACV2B,aAAa,EAAE,MAAM;gBACrBjB,UAAU,EAAE,GAAG;gBACfN,QAAQ,EAAE;cACZ,CAAE;cACFS,QAAQ,EAAE5D,OAAO,IAAIE,aAAc;cAAAmC,QAAA,EAElCrC,OAAO,gBACN1B,OAAA,CAAAE,SAAA;gBAAA6D,QAAA,gBACE/D,OAAA,CAACd,gBAAgB;kBACfgH,IAAI,EAAE,EAAG;kBACT1C,EAAE,EAAE;oBAAEoB,KAAK,EAAE,cAAc;oBAAEyB,EAAE,EAAE;kBAAE;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,uBAEJ;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjF,OAAA,CAACf,GAAG;QAACuE,EAAE,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAE6B,EAAE,EAAE;QAAE,CAAE;QAAApC,QAAA,eACtC/D,OAAA,CAACjB,UAAU;UAACmG,OAAO,EAAC,OAAO;UAAC1B,EAAE,EAAE;YAAEoB,KAAK,EAAE;UAAiB,CAAE;UAAAb,QAAA,GAAC,0BACnC,EAAC,GAAG,eAC5B/D,OAAA,CAACxB,IAAI;YACH8E,SAAS,EAAE7E,UAAW;YACtB6H,EAAE,EAAC,QAAQ;YACX9C,EAAE,EAAE;cACFoB,KAAK,EAAE,cAAc;cACrB2B,cAAc,EAAE,MAAM;cACtBpB,UAAU,EAAE,GAAG;cACf,SAAS,EAAE;gBACToB,cAAc,EAAE;cAClB;YACF,CAAE;YAAAxC,QAAA,EACH;UAED;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAERjF,OAAA,CAACb,QAAQ;MACPqH,IAAI,EAAExE,SAAU;MAChByE,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMzE,YAAY,CAAC,KAAK,CAAE;MACnC0E,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA9C,QAAA,eAExD/D,OAAA,CAACZ,KAAK;QACJ0H,QAAQ,EAAC,OAAO;QAChBJ,OAAO,EAAEA,CAAA,KAAMzE,YAAY,CAAC,KAAK,CAAE;QACnCuB,EAAE,EAAE;UAAEU,KAAK,EAAE;QAAO,CAAE;QAAAH,QAAA,EAErBjC;MAAK;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAACjE,EAAA,CAtQID,MAAM;EAAA,QACOxC,WAAW,EACde,QAAQ,EACLC,aAAa,EAU1Bb,OAAO;AAAA;AAAAqI,EAAA,GAbPhG,MAAM;AAwQZ,eAAeA,MAAM;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}