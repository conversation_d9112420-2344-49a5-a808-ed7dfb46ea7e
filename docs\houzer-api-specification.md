# Houzer Authentication API Specification

## Overview
This document defines the authentication and verification API endpoints following the Houzer application architecture pattern with OTP-based verification, Google signup, and comprehensive audit logging.

## Base Configuration

### Base URLs
```
Production: https://api.houzer.com
Development: http://localhost:8080
```

### API Versioning
- **Public APIs**: `/public/api/v1/`
- **Protected APIs**: `/api/v1/`

### Standard Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

## API Endpoints

### 1. Send OTP Endpoint [Email]
- **Path**: `POST /public/api/v1/verification/send-otp`
- **Purpose**: Sends OTP to a specified contact (email, mobile number)
- **Request**:
  - **Fields**: `contactType`, `contactValue`, `ipAddress`
- **Response**: `201 Created`
- **MIME Type**: `application/vnd.chidhagni-houzer.verification.send-otp.req-v1+json`
- **Database**: Logs action in Individual Verification Audit table

#### Request Body
```json
{
  "contactType": "EMAIL",
  "contactValue": "<EMAIL>",
  "ipAddress": "***********"
}
```

#### Response Body (201 Created)
```json
{
  "success": true,
  "data": {
    "verificationId": "ver_123456789",
    "contactType": "EMAIL",
    "contactValue": "u***@example.com",
    "expiresIn": 300,
    "message": "OTP sent successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 2. Verify OTP Endpoint [Email]
- **Path**: `POST /public/api/v1/verification/verify-otp`
- **Purpose**: Verifies the OTP received for the contact
- **Request**:
  - **Fields**: `contactType`, `contactValue`, `otp`
- **Response**: `200 OK`
- **MIME Type**: `application/vnd.chidhagni-houzer.verification.verify-otp.req-v1+json`
- **Database**: Logs action in Individual Verification Audit table

#### Request Body
```json
{
  "contactType": "EMAIL",
  "contactValue": "<EMAIL>",
  "otp": "123456"
}
```

#### Response Body (200 OK)
```json
{
  "success": true,
  "data": {
    "verificationId": "ver_123456789",
    "contactType": "EMAIL",
    "contactValue": "u***@example.com",
    "verified": true,
    "verificationToken": "vt_abcdef123456",
    "message": "OTP verified successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 3. Resend OTP Endpoint
- **Path**: `POST /public/api/v1/verification/resend-otp`
- **Purpose**: Resends the OTP to the specified contact
- **Request**:
  - **Fields**: `contactType`, `contactValue`
- **Response**: `201 Created`
- **MIME Type**: `application/vnd.chidhagni-houzer.verification.resend-otp.req-v1+json`
- **Database**: Logs resend action in Individual Verification Audit table

#### Request Body
```json
{
  "contactType": "EMAIL",
  "contactValue": "<EMAIL>"
}
```

#### Response Body (201 Created)
```json
{
  "success": true,
  "data": {
    "verificationId": "ver_123456789",
    "contactType": "EMAIL",
    "contactValue": "u***@example.com",
    "expiresIn": 300,
    "attemptsRemaining": 2,
    "message": "OTP resent successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 4. Google Signup Endpoint
- **Path**: `POST /public/api/v1/auth/google-signup`
- **Purpose**: Signs up an individual using Google and initiates the verification process
- **Consumes**: Google OAuth token and organization details
- **Produces**: `201 Created` status
- **MIME Type**: `application/vnd.chidhagni-houzer.auth.google-signup.req-v1+json`
- **Database**: Logs action in Individual Verification Audit, Individual, Organization tables

#### Request Body
```json
{
  "googleToken": "ya29.a0AfH6SMC...",
  "organizationName": "Tech Solutions Inc",
  "organizationType": "PRIVATE_LIMITED",
  "mobileNumber": "+1234567890",
  "acceptTerms": true,
  "marketingConsent": false
}
```

#### Response Body (201 Created)
```json
{
  "success": true,
  "data": {
    "individualId": "ind_123456789",
    "organizationId": "org_987654321",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "organizationName": "Tech Solutions Inc",
    "verificationRequired": false,
    "status": "ACTIVE",
    "message": "Google signup completed successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 5. Signup Endpoint
- **Path**: `POST /public/api/v1/signup`
- **Purpose**: Creates entries in Individual and Organization tables after email verification
- **Consumes**: `signupRequestDTO` with firstName, lastName, email, organizationName, organizationType, mobileNumber
- **Produces**: `201 Created` status and `signupResponseDTO`
- **MIME Type**: `application/vnd.chidhagni-houzer.signup.req-v1+json`
- **Database**: Logs action in Individual, Organization tables

#### Request Body
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "organizationName": "Tech Solutions Inc",
  "organizationType": "PRIVATE_LIMITED",
  "mobileNumber": "+1234567890",
  "verificationToken": "vt_abcdef123456",
  "acceptTerms": true,
  "marketingConsent": false
}
```

#### Response Body (201 Created)
```json
{
  "success": true,
  "data": {
    "individualId": "ind_123456789",
    "organizationId": "org_987654321",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "organizationName": "Tech Solutions Inc",
    "organizationType": "PRIVATE_LIMITED",
    "status": "PENDING_VERIFICATION",
    "emailVerified": true,
    "mobileVerified": false,
    "message": "Signup completed successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 6. Forgot Password Endpoint - Send Password Reset Link
- **Path**: `POST /public/api/v1/forgot-password`
- **Purpose**: Sends a password reset link to the user's email
- **Consumes**: Email address
- **Produces**: `200 OK` status
- **MIME Type**: `application/vnd.chidhagni-houzer.forgot-password.req-v1+json`
- **Database**: Logs action in Individual Password Reset Audit, Individual tables

#### Request Body
```json
{
  "email": "<EMAIL>",
  "ipAddress": "***********"
}
```

#### Response Body (200 OK)
```json
{
  "success": true,
  "data": {
    "email": "j***@example.com",
    "resetId": "rst_123456789",
    "expiresIn": 3600,
    "message": "Password reset link sent to your email"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 7. Reset Password Endpoint
- **Path**: `POST /public/api/v1/reset-password`
- **Purpose**: Validates the password reset code and saves the new password
- **Consumes**: Reset token and new password
- **Produces**: `200 OK` status
- **MIME Type**: `application/vnd.chidhagni-houzer.reset-password.req-v1+json`
- **Database**: Logs action in Individual Password Reset Audit, Individual tables

#### Request Body
```json
{
  "resetToken": "rst_token_123456789",
  "newPassword": "NewSecurePassword123!",
  "confirmPassword": "NewSecurePassword123!"
}
```

#### Response Body (200 OK)
```json
{
  "success": true,
  "data": {
    "individualId": "ind_123456789",
    "email": "j***@example.com",
    "passwordResetAt": "2024-01-15T10:30:00Z",
    "message": "Password reset successfully"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 8. Login Endpoint
- **Path**: `POST /api/v1/login`
- **Purpose**: Authenticates the user and provides an access token
- **Consumes**: Email/username and password
- **Produces**: `200 OK` status and `loginResponseDTO` with accessToken and refresh token
- **MIME Type**: `application/vnd.chidhagni-houzer.login.req-v1+json`
- **Database**: Logs action in Individual table

#### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "rememberMe": false,
  "deviceInfo": {
    "deviceId": "device_123456789",
    "deviceName": "iPhone 12",
    "platform": "iOS",
    "appVersion": "1.0.0"
  },
  "ipAddress": "***********"
}
```

#### Response Body (200 OK)
```json
{
  "success": true,
  "data": {
    "individual": {
      "individualId": "ind_123456789",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "status": "ACTIVE",
      "emailVerified": true,
      "mobileVerified": true,
      "roles": ["USER"],
      "permissions": ["profile:read", "profile:update"]
    },
    "organization": {
      "organizationId": "org_987654321",
      "organizationName": "Tech Solutions Inc",
      "organizationType": "PRIVATE_LIMITED",
      "status": "ACTIVE"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600,
      "tokenType": "Bearer"
    },
    "lastLoginAt": "2024-01-15T10:30:00Z",
    "message": "Login successful"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

## Database Schema Design

### Core Tables

#### 1. Individual Table
```sql
CREATE TABLE individual (
    individual_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    password_hash VARCHAR(255),
    salt VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    mobile_number VARCHAR(20),
    mobile_verified BOOLEAN DEFAULT FALSE,
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION') DEFAULT 'PENDING_VERIFICATION',
    google_id VARCHAR(255) UNIQUE,
    profile_picture_url VARCHAR(500),
    last_login_at TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    INDEX idx_individual_email (email),
    INDEX idx_individual_status (status),
    INDEX idx_individual_google_id (google_id)
);
```

#### 2. Organization Table
```sql
CREATE TABLE organization (
    organization_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_name VARCHAR(255) NOT NULL,
    organization_type ENUM('PRIVATE_LIMITED', 'PUBLIC_LIMITED', 'PARTNERSHIP', 'SOLE_PROPRIETORSHIP', 'LLP', 'NGO', 'GOVERNMENT', 'OTHER') NOT NULL,
    registration_number VARCHAR(100),
    tax_id VARCHAR(100),
    address_line_1 VARCHAR(255),
    address_line_2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'India',
    phone_number VARCHAR(20),
    website_url VARCHAR(255),
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,

    INDEX idx_organization_name (organization_name),
    INDEX idx_organization_type (organization_type),
    INDEX idx_organization_status (status)
);
```

#### 3. Individual Organization Mapping Table
```sql
CREATE TABLE individual_organization (
    individual_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    role ENUM('OWNER', 'ADMIN', 'MANAGER', 'EMPLOYEE', 'VIEWER') DEFAULT 'OWNER',
    is_primary BOOLEAN DEFAULT TRUE,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',

    PRIMARY KEY (individual_id, organization_id),
    FOREIGN KEY (individual_id) REFERENCES individual(individual_id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organization(organization_id) ON DELETE CASCADE,
    INDEX idx_individual_org_role (role),
    INDEX idx_individual_org_status (status)
);
```

### Verification and Audit Tables

#### 4. Individual Verification Audit Table
```sql
CREATE TABLE individual_verification_audit (
    audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    individual_id UUID,
    contact_type ENUM('EMAIL', 'MOBILE') NOT NULL,
    contact_value VARCHAR(255) NOT NULL,
    verification_type ENUM('SIGNUP', 'LOGIN', 'PASSWORD_RESET', 'PROFILE_UPDATE') NOT NULL,
    otp_code VARCHAR(10),
    otp_hash VARCHAR(255),
    verification_token VARCHAR(255),
    status ENUM('SENT', 'VERIFIED', 'EXPIRED', 'FAILED', 'RESENT') NOT NULL,
    attempts_count INTEGER DEFAULT 1,
    max_attempts INTEGER DEFAULT 3,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (individual_id) REFERENCES individual(individual_id) ON DELETE SET NULL,
    INDEX idx_verification_audit_individual (individual_id),
    INDEX idx_verification_audit_contact (contact_type, contact_value),
    INDEX idx_verification_audit_status (status),
    INDEX idx_verification_audit_expires (expires_at)
);
```

#### 5. Individual Password Reset Audit Table
```sql
CREATE TABLE individual_password_reset_audit (
    audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    individual_id UUID NOT NULL,
    email VARCHAR(255) NOT NULL,
    reset_token VARCHAR(255) NOT NULL,
    reset_token_hash VARCHAR(255) NOT NULL,
    status ENUM('REQUESTED', 'USED', 'EXPIRED', 'CANCELLED') DEFAULT 'REQUESTED',
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (individual_id) REFERENCES individual(individual_id) ON DELETE CASCADE,
    INDEX idx_password_reset_individual (individual_id),
    INDEX idx_password_reset_token (reset_token_hash),
    INDEX idx_password_reset_status (status),
    INDEX idx_password_reset_expires (expires_at)
);
```

#### 6. Individual Session Table
```sql
CREATE TABLE individual_session (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    individual_id UUID NOT NULL,
    refresh_token_hash VARCHAR(255) NOT NULL,
    device_info JSON,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    revoked_at TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (individual_id) REFERENCES individual(individual_id) ON DELETE CASCADE,
    INDEX idx_session_individual (individual_id),
    INDEX idx_session_token (refresh_token_hash),
    INDEX idx_session_expires (expires_at),
    INDEX idx_session_activity (last_activity_at)
);
```

### Role-Based Access Control Tables

#### 7. Roles Table
```sql
CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_name VARCHAR(100) UNIQUE NOT NULL,
    role_description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    organization_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (organization_id) REFERENCES organization(organization_id) ON DELETE CASCADE,
    INDEX idx_roles_name (role_name),
    INDEX idx_roles_organization (organization_id)
);
```

#### 8. Permissions Table
```sql
CREATE TABLE permissions (
    permission_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    permission_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_permissions_name (permission_name),
    INDEX idx_permissions_resource (resource),
    INDEX idx_permissions_action (action)
);
```

#### 9. Role Permissions Table
```sql
CREATE TABLE role_permissions (
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID,

    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES individual(individual_id) ON DELETE SET NULL
);
```

#### 10. Individual Roles Table
```sql
CREATE TABLE individual_roles (
    individual_id UUID NOT NULL,
    role_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    assigned_by UUID,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    status ENUM('ACTIVE', 'INACTIVE', 'EXPIRED') DEFAULT 'ACTIVE',

    PRIMARY KEY (individual_id, role_id, organization_id),
    FOREIGN KEY (individual_id) REFERENCES individual(individual_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organization(organization_id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES individual(individual_id) ON DELETE SET NULL,
    INDEX idx_individual_roles_expires (expires_at),
    INDEX idx_individual_roles_status (status)
);
```

## Data Transfer Objects (DTOs)

### Request DTOs

#### SendOtpRequestDTO
```typescript
interface SendOtpRequestDTO {
  contactType: 'EMAIL' | 'MOBILE';
  contactValue: string;
  ipAddress: string;
}
```

#### VerifyOtpRequestDTO
```typescript
interface VerifyOtpRequestDTO {
  contactType: 'EMAIL' | 'MOBILE';
  contactValue: string;
  otp: string;
}
```

#### ResendOtpRequestDTO
```typescript
interface ResendOtpRequestDTO {
  contactType: 'EMAIL' | 'MOBILE';
  contactValue: string;
}
```

#### GoogleSignupRequestDTO
```typescript
interface GoogleSignupRequestDTO {
  googleToken: string;
  organizationName: string;
  organizationType: OrganizationType;
  mobileNumber: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}
```

#### SignupRequestDTO
```typescript
interface SignupRequestDTO {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  organizationName: string;
  organizationType: OrganizationType;
  mobileNumber: string;
  verificationToken: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}
```

#### LoginRequestDTO
```typescript
interface LoginRequestDTO {
  email: string;
  password: string;
  rememberMe?: boolean;
  deviceInfo?: DeviceInfo;
  ipAddress: string;
}

interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  platform: string;
  appVersion: string;
}
```

### Response DTOs

#### SignupResponseDTO
```typescript
interface SignupResponseDTO {
  individualId: string;
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  organizationType: OrganizationType;
  status: IndividualStatus;
  emailVerified: boolean;
  mobileVerified: boolean;
  message: string;
}
```

#### LoginResponseDTO
```typescript
interface LoginResponseDTO {
  individual: IndividualDTO;
  organization: OrganizationDTO;
  tokens: TokensDTO;
  lastLoginAt: string;
  message: string;
}

interface IndividualDTO {
  individualId: string;
  email: string;
  firstName: string;
  lastName: string;
  status: IndividualStatus;
  emailVerified: boolean;
  mobileVerified: boolean;
  roles: string[];
  permissions: string[];
}

interface OrganizationDTO {
  organizationId: string;
  organizationName: string;
  organizationType: OrganizationType;
  status: OrganizationStatus;
}

interface TokensDTO {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}
```

### Enums

```typescript
enum OrganizationType {
  PRIVATE_LIMITED = 'PRIVATE_LIMITED',
  PUBLIC_LIMITED = 'PUBLIC_LIMITED',
  PARTNERSHIP = 'PARTNERSHIP',
  SOLE_PROPRIETORSHIP = 'SOLE_PROPRIETORSHIP',
  LLP = 'LLP',
  NGO = 'NGO',
  GOVERNMENT = 'GOVERNMENT',
  OTHER = 'OTHER'
}

enum IndividualStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION'
}

enum OrganizationStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

enum ContactType {
  EMAIL = 'EMAIL',
  MOBILE = 'MOBILE'
}

enum VerificationStatus {
  SENT = 'SENT',
  VERIFIED = 'VERIFIED',
  EXPIRED = 'EXPIRED',
  FAILED = 'FAILED',
  RESENT = 'RESENT'
}
```

## Error Codes and Messages

### Verification Errors
- `VER_001`: Invalid contact type
- `VER_002`: Invalid contact value format
- `VER_003`: OTP expired
- `VER_004`: Invalid OTP
- `VER_005`: Maximum OTP attempts exceeded
- `VER_006`: OTP already verified
- `VER_007`: Verification token invalid or expired

### Authentication Errors
- `AUTH_001`: Invalid credentials
- `AUTH_002`: Account locked due to multiple failed attempts
- `AUTH_003`: Account not verified
- `AUTH_004`: Account suspended
- `AUTH_005`: Invalid or expired token
- `AUTH_006`: Google authentication failed
- `AUTH_007`: Email already registered
- `AUTH_008`: Organization already exists

### Validation Errors
- `VAL_001`: Required field missing
- `VAL_002`: Invalid email format
- `VAL_003`: Invalid mobile number format
- `VAL_004`: Password does not meet requirements
- `VAL_005`: Invalid organization type
- `VAL_006`: Terms and conditions not accepted

### Rate Limiting Errors
- `RATE_001`: Too many OTP requests
- `RATE_002`: Too many login attempts
- `RATE_003`: Too many password reset requests

## Validation Rules

### Email Validation
- Must be a valid email format
- Maximum length: 255 characters
- Case insensitive

### Mobile Number Validation
- Must include country code
- Format: +[country_code][number]
- Example: +************

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character (@$!%*?&)

### OTP Configuration
- Length: 6 digits
- Expiry: 5 minutes (300 seconds)
- Maximum attempts: 3
- Resend cooldown: 30 seconds

## Rate Limiting Configuration

### OTP Endpoints
- Send OTP: 3 requests per 15 minutes per IP
- Verify OTP: 5 attempts per OTP
- Resend OTP: 3 requests per hour per contact

### Authentication Endpoints
- Login: 5 attempts per 15 minutes per IP
- Signup: 3 requests per hour per IP
- Password Reset: 3 requests per hour per email

## Security Considerations

### OTP Security
- OTPs are hashed before storage
- Automatic cleanup of expired OTPs
- Rate limiting to prevent brute force attacks
- IP address logging for audit trails

### Password Security
- Passwords are hashed using bcrypt with salt
- Minimum password strength requirements
- Account lockout after failed attempts
- Password reset tokens expire after 1 hour

### Token Security
- JWT tokens with short expiration (15 minutes)
- Refresh tokens with longer expiration (7 days)
- Device-based session management
- Automatic token cleanup on logout

### Audit Logging
- All verification attempts logged
- Failed login attempts tracked
- Password reset activities monitored
- IP address and user agent captured
