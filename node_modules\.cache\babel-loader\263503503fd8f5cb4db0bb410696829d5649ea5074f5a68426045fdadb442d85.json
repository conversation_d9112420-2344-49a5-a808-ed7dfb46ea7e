{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:8080/api/v1';\n\n// Helper function to get authorization headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem('token');\n  return {\n    'Content-Type': 'application/json',\n    ...(token && {\n      Authorization: `Bearer ${token}`\n    })\n  };\n};\nconst authService = {\n  login: async (credentials, errorCallback, successCallback) => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/login`, credentials, {\n        headers: getAuthHeaders()\n      });\n      if (response.data.token) {\n        localStorage.setItem('token', response.data.token);\n      }\n      if (successCallback) {\n        successCallback(response.data);\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'Login failed';\n      if (errorCallback) {\n        errorCallback(errorMessage);\n      }\n      throw errorMessage;\n    }\n  },\n  // Google OAuth login\n  googleLogin: async (googleData, errorCallback, successCallback) => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/google`, {\n        token: googleData.token,\n        user: googleData.user\n      }, {\n        headers: getAuthHeaders()\n      });\n      if (response.data.token) {\n        localStorage.setItem('token', response.data.token);\n        // Store user info for quick access\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      if (successCallback) {\n        successCallback(response.data);\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'Google login failed';\n      if (errorCallback) {\n        errorCallback(errorMessage);\n      }\n      throw errorMessage;\n    }\n  },\n  signup: async (userData, errorCallback, successCallback) => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/signup`, userData, {\n        headers: getAuthHeaders()\n      });\n      if (successCallback) {\n        successCallback(response.data);\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || 'Signup failed';\n      if (errorCallback) {\n        errorCallback(errorMessage);\n      }\n      throw errorMessage;\n    }\n  },\n  forgotPassword: async (email, errorCallback, successCallback) => {\n    try {\n      const response = await axios.post(`${API_BASE_URL}/forgot-password`, {\n        email\n      }, {\n        headers: getAuthHeaders()\n      });\n      if (successCallback) {\n        successCallback(response.data);\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const errorMessage = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || error.message || 'Password reset failed';\n      if (errorCallback) {\n        errorCallback(errorMessage);\n      }\n      throw errorMessage;\n    }\n  },\n  getProfile: async (errorCallback, successCallback) => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/profile`, {\n        headers: getAuthHeaders()\n      });\n      if (successCallback) {\n        successCallback(response.data);\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const errorMessage = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || error.message || 'Failed to fetch profile';\n      if (errorCallback) {\n        errorCallback(errorMessage);\n      }\n      throw errorMessage;\n    }\n  },\n  logout: () => {\n    localStorage.removeItem('token');\n  },\n  isAuthenticated: () => {\n    return !!localStorage.getItem('token');\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "getAuthHeaders", "token", "localStorage", "getItem", "Authorization", "authService", "login", "credentials", "<PERSON><PERSON><PERSON><PERSON>", "success<PERSON>allback", "response", "post", "headers", "data", "setItem", "error", "_error$response", "_error$response$data", "errorMessage", "message", "googleLogin", "googleData", "user", "JSON", "stringify", "_error$response2", "_error$response2$data", "signup", "userData", "_error$response3", "_error$response3$data", "forgotPassword", "email", "_error$response4", "_error$response4$data", "getProfile", "get", "_error$response5", "_error$response5$data", "logout", "removeItem", "isAuthenticated"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/api/authService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = 'http://localhost:8080/api/v1';\r\n\r\n// Helper function to get authorization headers\r\nconst getAuthHeaders = () => {\r\n  const token = localStorage.getItem('token');\r\n  return {\r\n    'Content-Type': 'application/json',\r\n    ...(token && { Authorization: `Bearer ${token}` })\r\n  };\r\n};\r\n\r\nconst authService = {\r\n  login: async (credentials, errorCallback, successCallback) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/login`, credentials, {\r\n        headers: getAuthHeaders()\r\n      });\r\n\r\n      if (response.data.token) {\r\n        localStorage.setItem('token', response.data.token);\r\n      }\r\n\r\n      if (successCallback) {\r\n        successCallback(response.data);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || error.message || 'Login failed';\r\n      if (errorCallback) {\r\n        errorCallback(errorMessage);\r\n      }\r\n      throw errorMessage;\r\n    }\r\n  },\r\n\r\n  // Google OAuth login\r\n  googleLogin: async (googleData, errorCallback, successCallback) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/auth/google`, {\r\n        token: googleData.token,\r\n        user: googleData.user\r\n      }, {\r\n        headers: getAuthHeaders()\r\n      });\r\n\r\n      if (response.data.token) {\r\n        localStorage.setItem('token', response.data.token);\r\n        // Store user info for quick access\r\n        localStorage.setItem('user', JSON.stringify(response.data.user));\r\n      }\r\n\r\n      if (successCallback) {\r\n        successCallback(response.data);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || error.message || 'Google login failed';\r\n      if (errorCallback) {\r\n        errorCallback(errorMessage);\r\n      }\r\n      throw errorMessage;\r\n    }\r\n  },\r\n\r\n  signup: async (userData, errorCallback, successCallback) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/signup`, userData, {\r\n        headers: getAuthHeaders()\r\n      });\r\n\r\n      if (successCallback) {\r\n        successCallback(response.data);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || error.message || 'Signup failed';\r\n      if (errorCallback) {\r\n        errorCallback(errorMessage);\r\n      }\r\n      throw errorMessage;\r\n    }\r\n  },\r\n\r\n  forgotPassword: async (email, errorCallback, successCallback) => {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/forgot-password`, { email }, {\r\n        headers: getAuthHeaders()\r\n      });\r\n\r\n      if (successCallback) {\r\n        successCallback(response.data);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || error.message || 'Password reset failed';\r\n      if (errorCallback) {\r\n        errorCallback(errorMessage);\r\n      }\r\n      throw errorMessage;\r\n    }\r\n  },\r\n\r\n  getProfile: async (errorCallback, successCallback) => {\r\n    try {\r\n      const response = await axios.get(`${API_BASE_URL}/profile`, {\r\n        headers: getAuthHeaders()\r\n      });\r\n\r\n      if (successCallback) {\r\n        successCallback(response.data);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch profile';\r\n      if (errorCallback) {\r\n        errorCallback(errorMessage);\r\n      }\r\n      throw errorMessage;\r\n    }\r\n  },\r\n\r\n  logout: () => {\r\n    localStorage.removeItem('token');\r\n  },\r\n\r\n  isAuthenticated: () => {\r\n    return !!localStorage.getItem('token');\r\n  }\r\n};\r\n\r\nexport default authService; "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,8BAA8B;;AAEnD;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAO;IACL,cAAc,EAAE,kBAAkB;IAClC,IAAIF,KAAK,IAAI;MAAEG,aAAa,EAAE,UAAUH,KAAK;IAAG,CAAC;EACnD,CAAC;AACH,CAAC;AAED,MAAMI,WAAW,GAAG;EAClBC,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEC,aAAa,EAAEC,eAAe,KAAK;IAC5D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,GAAGZ,YAAY,QAAQ,EAAEQ,WAAW,EAAE;QACtEK,OAAO,EAAEZ,cAAc,CAAC;MAC1B,CAAC,CAAC;MAEF,IAAIU,QAAQ,CAACG,IAAI,CAACZ,KAAK,EAAE;QACvBC,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACG,IAAI,CAACZ,KAAK,CAAC;MACpD;MAEA,IAAIQ,eAAe,EAAE;QACnBA,eAAe,CAACC,QAAQ,CAACG,IAAI,CAAC;MAChC;MAEA,OAAOH,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAD,KAAK,CAACL,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAIJ,KAAK,CAACI,OAAO,IAAI,cAAc;MACrF,IAAIX,aAAa,EAAE;QACjBA,aAAa,CAACU,YAAY,CAAC;MAC7B;MACA,MAAMA,YAAY;IACpB;EACF,CAAC;EAED;EACAE,WAAW,EAAE,MAAAA,CAAOC,UAAU,EAAEb,aAAa,EAAEC,eAAe,KAAK;IACjE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,GAAGZ,YAAY,cAAc,EAAE;QAC/DE,KAAK,EAAEoB,UAAU,CAACpB,KAAK;QACvBqB,IAAI,EAAED,UAAU,CAACC;MACnB,CAAC,EAAE;QACDV,OAAO,EAAEZ,cAAc,CAAC;MAC1B,CAAC,CAAC;MAEF,IAAIU,QAAQ,CAACG,IAAI,CAACZ,KAAK,EAAE;QACvBC,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACG,IAAI,CAACZ,KAAK,CAAC;QAClD;QACAC,YAAY,CAACY,OAAO,CAAC,MAAM,EAAES,IAAI,CAACC,SAAS,CAACd,QAAQ,CAACG,IAAI,CAACS,IAAI,CAAC,CAAC;MAClE;MAEA,IAAIb,eAAe,EAAE;QACnBA,eAAe,CAACC,QAAQ,CAACG,IAAI,CAAC;MAChC;MAEA,OAAOH,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAU,gBAAA,EAAAC,qBAAA;MACd,MAAMR,YAAY,GAAG,EAAAO,gBAAA,GAAAV,KAAK,CAACL,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAIJ,KAAK,CAACI,OAAO,IAAI,qBAAqB;MAC5F,IAAIX,aAAa,EAAE;QACjBA,aAAa,CAACU,YAAY,CAAC;MAC7B;MACA,MAAMA,YAAY;IACpB;EACF,CAAC;EAEDS,MAAM,EAAE,MAAAA,CAAOC,QAAQ,EAAEpB,aAAa,EAAEC,eAAe,KAAK;IAC1D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,GAAGZ,YAAY,SAAS,EAAE6B,QAAQ,EAAE;QACpEhB,OAAO,EAAEZ,cAAc,CAAC;MAC1B,CAAC,CAAC;MAEF,IAAIS,eAAe,EAAE;QACnBA,eAAe,CAACC,QAAQ,CAACG,IAAI,CAAC;MAChC;MAEA,OAAOH,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACd,MAAMZ,YAAY,GAAG,EAAAW,gBAAA,GAAAd,KAAK,CAACL,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBX,OAAO,KAAIJ,KAAK,CAACI,OAAO,IAAI,eAAe;MACtF,IAAIX,aAAa,EAAE;QACjBA,aAAa,CAACU,YAAY,CAAC;MAC7B;MACA,MAAMA,YAAY;IACpB;EACF,CAAC;EAEDa,cAAc,EAAE,MAAAA,CAAOC,KAAK,EAAExB,aAAa,EAAEC,eAAe,KAAK;IAC/D,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,GAAGZ,YAAY,kBAAkB,EAAE;QAAEiC;MAAM,CAAC,EAAE;QAC9EpB,OAAO,EAAEZ,cAAc,CAAC;MAC1B,CAAC,CAAC;MAEF,IAAIS,eAAe,EAAE;QACnBA,eAAe,CAACC,QAAQ,CAACG,IAAI,CAAC;MAChC;MAEA,OAAOH,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAkB,gBAAA,EAAAC,qBAAA;MACd,MAAMhB,YAAY,GAAG,EAAAe,gBAAA,GAAAlB,KAAK,CAACL,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAIJ,KAAK,CAACI,OAAO,IAAI,uBAAuB;MAC9F,IAAIX,aAAa,EAAE;QACjBA,aAAa,CAACU,YAAY,CAAC;MAC7B;MACA,MAAMA,YAAY;IACpB;EACF,CAAC;EAEDiB,UAAU,EAAE,MAAAA,CAAO3B,aAAa,EAAEC,eAAe,KAAK;IACpD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACsC,GAAG,CAAC,GAAGrC,YAAY,UAAU,EAAE;QAC1Da,OAAO,EAAEZ,cAAc,CAAC;MAC1B,CAAC,CAAC;MAEF,IAAIS,eAAe,EAAE;QACnBA,eAAe,CAACC,QAAQ,CAACG,IAAI,CAAC;MAChC;MAEA,OAAOH,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MACd,MAAMpB,YAAY,GAAG,EAAAmB,gBAAA,GAAAtB,KAAK,CAACL,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAIJ,KAAK,CAACI,OAAO,IAAI,yBAAyB;MAChG,IAAIX,aAAa,EAAE;QACjBA,aAAa,CAACU,YAAY,CAAC;MAC7B;MACA,MAAMA,YAAY;IACpB;EACF,CAAC;EAEDqB,MAAM,EAAEA,CAAA,KAAM;IACZrC,YAAY,CAACsC,UAAU,CAAC,OAAO,CAAC;EAClC,CAAC;EAEDC,eAAe,EAAEA,CAAA,KAAM;IACrB,OAAO,CAAC,CAACvC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACxC;AACF,CAAC;AAED,eAAeE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}