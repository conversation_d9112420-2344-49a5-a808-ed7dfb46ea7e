import React from 'react';
import ReactDOM from 'react-dom/client';
import { gapi } from 'gapi-script';
import App from './App';
import { GOOGLE_OAUTH_CONFIG } from './config/googleOAuth';

// Initialize Google API when the app starts
const initializeGapi = async () => {
  try {
    await gapi.load('auth2', () => {
      gapi.auth2.init({
        client_id: GOOGLE_OAUTH_CONFIG.clientId,
      });
    });
  } catch (error) {
    console.error('Error initializing Google API:', error);
  }
};

// Initialize Google API
initializeGapi();

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);