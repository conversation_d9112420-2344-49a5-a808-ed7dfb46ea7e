# Houzer Authentication System Implementation Guide

## Overview
This guide provides step-by-step implementation instructions for the Houzer-style authentication system with OTP verification, Google signup, and comprehensive audit logging.

## Project Structure

```
houzer-auth-backend/
├── src/
│   ├── controllers/
│   │   ├── verification.controller.ts
│   │   ├── auth.controller.ts
│   │   └── individual.controller.ts
│   ├── services/
│   │   ├── verification.service.ts
│   │   ├── auth.service.ts
│   │   ├── email.service.ts
│   │   ├── sms.service.ts
│   │   └── google.service.ts
│   ├── middleware/
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   ├── rate-limit.middleware.ts
│   │   └── audit.middleware.ts
│   ├── models/
│   │   ├── individual.model.ts
│   │   ├── organization.model.ts
│   │   ├── verification.model.ts
│   │   └── session.model.ts
│   ├── dto/
│   │   ├── verification.dto.ts
│   │   ├── auth.dto.ts
│   │   └── common.dto.ts
│   ├── utils/
│   │   ├── otp.util.ts
│   │   ├── password.util.ts
│   │   ├── jwt.util.ts
│   │   └── validation.util.ts
│   ├── config/
│   │   ├── database.config.ts
│   │   ├── email.config.ts
│   │   └── google.config.ts
│   └── app.ts
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
└── package.json
```

## Core Service Implementations

### 1. OTP Verification Service

```typescript
// src/services/verification.service.ts
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { EmailService } from './email.service';
import { SMSService } from './sms.service';

export class VerificationService {
  private prisma = new PrismaClient();
  private emailService = new EmailService();
  private smsService = new SMSService();

  async sendOTP(contactType: string, contactValue: string, ipAddress: string): Promise<any> {
    // Generate 6-digit OTP
    const otp = this.generateOTP();
    const otpHash = await bcrypt.hash(otp, 10);
    
    // Check rate limiting
    await this.checkRateLimit(contactType, contactValue, ipAddress);
    
    // Create verification record
    const verification = await this.prisma.individualVerificationAudit.create({
      data: {
        contactType: contactType as any,
        contactValue,
        verificationType: 'SIGNUP',
        otpHash,
        status: 'SENT',
        expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
        ipAddress,
        attemptsCount: 1,
        maxAttempts: 3
      }
    });

    // Send OTP via appropriate channel
    if (contactType === 'EMAIL') {
      await this.emailService.sendOTP(contactValue, otp);
    } else if (contactType === 'MOBILE') {
      await this.smsService.sendOTP(contactValue, otp);
    }

    return {
      verificationId: verification.auditId,
      contactType,
      contactValue: this.maskContactValue(contactType, contactValue),
      expiresIn: 300,
      message: 'OTP sent successfully'
    };
  }

  async verifyOTP(contactType: string, contactValue: string, otp: string): Promise<any> {
    // Find latest verification record
    const verification = await this.prisma.individualVerificationAudit.findFirst({
      where: {
        contactType: contactType as any,
        contactValue,
        status: 'SENT',
        expiresAt: { gt: new Date() }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (!verification) {
      throw new Error('VER_003: OTP expired or not found');
    }

    // Check attempts
    if (verification.attemptsCount >= verification.maxAttempts) {
      await this.prisma.individualVerificationAudit.update({
        where: { auditId: verification.auditId },
        data: { status: 'FAILED' }
      });
      throw new Error('VER_005: Maximum OTP attempts exceeded');
    }

    // Verify OTP
    const isValid = await bcrypt.compare(otp, verification.otpHash!);
    
    if (!isValid) {
      await this.prisma.individualVerificationAudit.update({
        where: { auditId: verification.auditId },
        data: { 
          attemptsCount: verification.attemptsCount + 1,
          status: verification.attemptsCount + 1 >= verification.maxAttempts ? 'FAILED' : 'SENT'
        }
      });
      throw new Error('VER_004: Invalid OTP');
    }

    // Mark as verified and generate verification token
    const verificationToken = this.generateVerificationToken();
    
    await this.prisma.individualVerificationAudit.update({
      where: { auditId: verification.auditId },
      data: {
        status: 'VERIFIED',
        verifiedAt: new Date(),
        verificationToken
      }
    });

    return {
      verificationId: verification.auditId,
      contactType,
      contactValue: this.maskContactValue(contactType, contactValue),
      verified: true,
      verificationToken,
      message: 'OTP verified successfully'
    };
  }

  async resendOTP(contactType: string, contactValue: string): Promise<any> {
    // Check if there's a recent verification
    const recentVerification = await this.prisma.individualVerificationAudit.findFirst({
      where: {
        contactType: contactType as any,
        contactValue,
        createdAt: { gt: new Date(Date.now() - 30 * 1000) } // 30 seconds cooldown
      }
    });

    if (recentVerification) {
      throw new Error('VER_008: Please wait before requesting another OTP');
    }

    // Generate new OTP
    const otp = this.generateOTP();
    const otpHash = await bcrypt.hash(otp, 10);

    // Update existing record or create new one
    const verification = await this.prisma.individualVerificationAudit.create({
      data: {
        contactType: contactType as any,
        contactValue,
        verificationType: 'SIGNUP',
        otpHash,
        status: 'RESENT',
        expiresAt: new Date(Date.now() + 5 * 60 * 1000),
        attemptsCount: 1,
        maxAttempts: 3
      }
    });

    // Send OTP
    if (contactType === 'EMAIL') {
      await this.emailService.sendOTP(contactValue, otp);
    } else if (contactType === 'MOBILE') {
      await this.smsService.sendOTP(contactValue, otp);
    }

    return {
      verificationId: verification.auditId,
      contactType,
      contactValue: this.maskContactValue(contactType, contactValue),
      expiresIn: 300,
      attemptsRemaining: 2,
      message: 'OTP resent successfully'
    };
  }

  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private generateVerificationToken(): string {
    const crypto = require('crypto');
    return `vt_${crypto.randomBytes(32).toString('hex')}`;
  }

  private maskContactValue(contactType: string, contactValue: string): string {
    if (contactType === 'EMAIL') {
      const [local, domain] = contactValue.split('@');
      return `${local.charAt(0)}***@${domain}`;
    } else if (contactType === 'MOBILE') {
      return `${contactValue.slice(0, 3)}***${contactValue.slice(-3)}`;
    }
    return contactValue;
  }

  private async checkRateLimit(contactType: string, contactValue: string, ipAddress: string): Promise<void> {
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    
    const recentAttempts = await this.prisma.individualVerificationAudit.count({
      where: {
        OR: [
          { contactValue, createdAt: { gt: fifteenMinutesAgo } },
          { ipAddress, createdAt: { gt: fifteenMinutesAgo } }
        ]
      }
    });

    if (recentAttempts >= 3) {
      throw new Error('RATE_001: Too many OTP requests');
    }
  }
}
```

### 2. Authentication Service

```typescript
// src/services/auth.service.ts
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { GoogleService } from './google.service';

export class AuthService {
  private prisma = new PrismaClient();
  private googleService = new GoogleService();

  async signup(signupData: any): Promise<any> {
    // Verify the verification token
    const verification = await this.prisma.individualVerificationAudit.findFirst({
      where: {
        verificationToken: signupData.verificationToken,
        status: 'VERIFIED',
        contactValue: signupData.email
      }
    });

    if (!verification) {
      throw new Error('VER_007: Verification token invalid or expired');
    }

    // Check if user already exists
    const existingUser = await this.prisma.individual.findUnique({
      where: { email: signupData.email }
    });

    if (existingUser) {
      throw new Error('AUTH_007: Email already registered');
    }

    // Hash password
    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(signupData.password, salt);

    // Create organization first
    const organization = await this.prisma.organization.create({
      data: {
        organizationName: signupData.organizationName,
        organizationType: signupData.organizationType,
        status: 'ACTIVE'
      }
    });

    // Create individual
    const individual = await this.prisma.individual.create({
      data: {
        email: signupData.email,
        emailVerified: true,
        passwordHash,
        salt,
        firstName: signupData.firstName,
        lastName: signupData.lastName,
        mobileNumber: signupData.mobileNumber,
        status: 'ACTIVE'
      }
    });

    // Link individual to organization
    await this.prisma.individualOrganization.create({
      data: {
        individualId: individual.individualId,
        organizationId: organization.organizationId,
        role: 'OWNER',
        isPrimary: true,
        status: 'ACTIVE'
      }
    });

    return {
      individualId: individual.individualId,
      organizationId: organization.organizationId,
      email: individual.email,
      firstName: individual.firstName,
      lastName: individual.lastName,
      organizationName: organization.organizationName,
      organizationType: organization.organizationType,
      status: individual.status,
      emailVerified: individual.emailVerified,
      mobileVerified: individual.mobileVerified,
      message: 'Signup completed successfully'
    };
  }

  async googleSignup(googleToken: string, organizationData: any): Promise<any> {
    // Verify Google token and get user info
    const googleUser = await this.googleService.verifyToken(googleToken);
    
    // Check if user already exists
    const existingUser = await this.prisma.individual.findFirst({
      where: {
        OR: [
          { email: googleUser.email },
          { googleId: googleUser.id }
        ]
      }
    });

    if (existingUser) {
      throw new Error('AUTH_007: Email already registered');
    }

    // Create organization
    const organization = await this.prisma.organization.create({
      data: {
        organizationName: organizationData.organizationName,
        organizationType: organizationData.organizationType,
        status: 'ACTIVE'
      }
    });

    // Create individual with Google data
    const individual = await this.prisma.individual.create({
      data: {
        email: googleUser.email,
        emailVerified: true,
        googleId: googleUser.id,
        firstName: googleUser.given_name,
        lastName: googleUser.family_name,
        mobileNumber: organizationData.mobileNumber,
        profilePictureUrl: googleUser.picture,
        status: 'ACTIVE'
      }
    });

    // Link individual to organization
    await this.prisma.individualOrganization.create({
      data: {
        individualId: individual.individualId,
        organizationId: organization.organizationId,
        role: 'OWNER',
        isPrimary: true,
        status: 'ACTIVE'
      }
    });

    return {
      individualId: individual.individualId,
      organizationId: organization.organizationId,
      email: individual.email,
      firstName: individual.firstName,
      lastName: individual.lastName,
      organizationName: organization.organizationName,
      verificationRequired: false,
      status: individual.status,
      message: 'Google signup completed successfully'
    };
  }

  async login(loginData: any): Promise<any> {
    // Find user
    const individual = await this.prisma.individual.findUnique({
      where: { email: loginData.email },
      include: {
        individualOrganization: {
          include: {
            organization: true
          },
          where: { isPrimary: true }
        }
      }
    });

    if (!individual) {
      throw new Error('AUTH_001: Invalid credentials');
    }

    // Check account status
    if (individual.status !== 'ACTIVE') {
      throw new Error('AUTH_004: Account suspended');
    }

    // Check if account is locked
    if (individual.lockedUntil && individual.lockedUntil > new Date()) {
      throw new Error('AUTH_002: Account locked due to multiple failed attempts');
    }

    // Verify password (skip for Google users)
    if (individual.passwordHash) {
      const isPasswordValid = await bcrypt.compare(loginData.password, individual.passwordHash);
      
      if (!isPasswordValid) {
        await this.handleFailedLogin(individual.individualId);
        throw new Error('AUTH_001: Invalid credentials');
      }
    }

    // Reset failed login attempts and update last login
    await this.prisma.individual.update({
      where: { individualId: individual.individualId },
      data: {
        failedLoginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date()
      }
    });

    // Generate tokens
    const tokens = await this.generateTokens(individual.individualId, loginData.deviceInfo);

    const primaryOrg = individual.individualOrganization[0];

    return {
      individual: {
        individualId: individual.individualId,
        email: individual.email,
        firstName: individual.firstName,
        lastName: individual.lastName,
        status: individual.status,
        emailVerified: individual.emailVerified,
        mobileVerified: individual.mobileVerified,
        roles: ['USER'], // TODO: Get from roles table
        permissions: ['profile:read', 'profile:update'] // TODO: Get from permissions
      },
      organization: primaryOrg ? {
        organizationId: primaryOrg.organization.organizationId,
        organizationName: primaryOrg.organization.organizationName,
        organizationType: primaryOrg.organization.organizationType,
        status: primaryOrg.organization.status
      } : null,
      tokens,
      lastLoginAt: individual.lastLoginAt,
      message: 'Login successful'
    };
  }

  private async generateTokens(individualId: string, deviceInfo?: any) {
    const accessToken = jwt.sign(
      { individualId, type: 'access' },
      process.env.JWT_SECRET!,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      { individualId, type: 'refresh' },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d' }
    );

    // Store refresh token
    await this.prisma.individualSession.create({
      data: {
        individualId,
        refreshTokenHash: await bcrypt.hash(refreshToken, 10),
        deviceInfo,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      }
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: 900, // 15 minutes
      tokenType: 'Bearer'
    };
  }

  private async handleFailedLogin(individualId: string) {
    const individual = await this.prisma.individual.findUnique({
      where: { individualId }
    });

    if (!individual) return;

    const failedAttempts = individual.failedLoginAttempts + 1;
    const updateData: any = { failedLoginAttempts: failedAttempts };

    // Lock account after 5 failed attempts
    if (failedAttempts >= 5) {
      updateData.lockedUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
    }

    await this.prisma.individual.update({
      where: { individualId },
      data: updateData
    });
  }
}
```
