{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\components\\\\OrDivider.js\";\nimport { Box, Divider, Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrDivider = ({\n  text = \"OR\",\n  sx = {}\n}) => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      my: 2,\n      ...sx\n    },\n    children: [/*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        flex: 1\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        px: 2,\n        color: 'text.secondary',\n        fontSize: '12px',\n        fontWeight: 500,\n        textTransform: 'uppercase',\n        letterSpacing: '0.5px'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        flex: 1\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = OrDivider;\nexport default OrDivider;\nvar _c;\n$RefreshReg$(_c, \"OrDivider\");", "map": {"version": 3, "names": ["Box", "Divider", "Typography", "jsxDEV", "_jsxDEV", "OrDivider", "text", "sx", "display", "alignItems", "my", "children", "flex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "px", "color", "fontSize", "fontWeight", "textTransform", "letterSpacing", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/components/OrDivider.js"], "sourcesContent": ["import { Box, Divider, Typography } from '@mui/material';\n\nconst OrDivider = ({ text = \"OR\", sx = {} }) => {\n  return (\n    <Box\n      sx={{\n        display: 'flex',\n        alignItems: 'center',\n        my: 2,\n        ...sx\n      }}\n    >\n      <Divider sx={{ flex: 1 }} />\n      <Typography\n        variant=\"body2\"\n        sx={{\n          px: 2,\n          color: 'text.secondary',\n          fontSize: '12px',\n          fontWeight: 500,\n          textTransform: 'uppercase',\n          letterSpacing: '0.5px'\n        }}\n      >\n        {text}\n      </Typography>\n      <Divider sx={{ flex: 1 }} />\n    </Box>\n  );\n};\n\nexport default OrDivider;\n"], "mappings": ";AAAA,SAASA,GAAG,EAAEC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI,GAAG,IAAI;EAAEC,EAAE,GAAG,CAAC;AAAE,CAAC,KAAK;EAC9C,oBACEH,OAAA,CAACJ,GAAG;IACFO,EAAE,EAAE;MACFC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE,CAAC;MACL,GAAGH;IACL,CAAE;IAAAI,QAAA,gBAEFP,OAAA,CAACH,OAAO;MAACM,EAAE,EAAE;QAAEK,IAAI,EAAE;MAAE;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5BZ,OAAA,CAACF,UAAU;MACTe,OAAO,EAAC,OAAO;MACfV,EAAE,EAAE;QACFW,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,gBAAgB;QACvBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE;MACjB,CAAE;MAAAZ,QAAA,EAEDL;IAAI;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACbZ,OAAA,CAACH,OAAO;MAACM,EAAE,EAAE;QAAEK,IAAI,EAAE;MAAE;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC;AAEV,CAAC;AAACQ,EAAA,GA3BInB,SAAS;AA6Bf,eAAeA,SAAS;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}