{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\components\\\\GoogleOAuthStatus.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { Alert, AlertTitle, Box, Typography, Link, Chip } from '@mui/material';\nimport { CheckCircle as CheckIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GoogleOAuthStatus = () => {\n  _s();\n  const [status, setStatus] = useState('checking');\n  const [message, setMessage] = useState('');\n  useEffect(() => {\n    checkGoogleOAuthSetup();\n  }, []);\n  const checkGoogleOAuthSetup = () => {\n    const clientId = GOOGLE_OAUTH_CONFIG.clientId;\n    if (!clientId || clientId === 'YOUR_GOOGLE_CLIENT_ID_HERE') {\n      setStatus('not-configured');\n      setMessage('Google OAuth is not configured. Please follow the setup guide.');\n    } else if (clientId.includes('your_google_client_id_here')) {\n      setStatus('not-configured');\n      setMessage('Please replace the placeholder with your actual Google Client ID.');\n    } else {\n      setStatus('configured');\n      setMessage('Google OAuth is properly configured and ready to use.');\n    }\n  };\n  const getAlertProps = () => {\n    switch (status) {\n      case 'configured':\n        return {\n          severity: 'success',\n          icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 17\n          }, this),\n          title: 'Google OAuth Ready'\n        };\n      case 'not-configured':\n        return {\n          severity: 'warning',\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 17\n          }, this),\n          title: 'Setup Required'\n        };\n      default:\n        return {\n          severity: 'info',\n          icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 17\n          }, this),\n          title: 'Checking Configuration'\n        };\n    }\n  };\n  const alertProps = getAlertProps();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mb: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Alert, {\n      severity: alertProps.severity,\n      icon: alertProps.icon,\n      sx: {\n        borderRadius: 2,\n        '& .MuiAlert-message': {\n          width: '100%'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n        sx: {\n          fontWeight: 600\n        },\n        children: alertProps.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          mb: 1\n        },\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), status === 'configured' && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: \"OAuth Ready\",\n          color: \"success\",\n          size: \"small\",\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Responsive Design\",\n          color: \"primary\",\n          size: \"small\",\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Mobile Optimized\",\n          color: \"secondary\",\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), status === 'not-configured' && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 1\n          },\n          children: \"To enable Google OAuth:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          component: \"div\",\n          children: [\"1. Follow the setup guide in\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            href: \"/GOOGLE_OAUTH_SETUP.md\",\n            target: \"_blank\",\n            sx: {\n              fontWeight: 600\n            },\n            children: \"GOOGLE_OAUTH_SETUP.md\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), \"2. Update your \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \".env\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 30\n          }, this), \" file with the Google Client ID\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), \"3. Restart the development server\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(GoogleOAuthStatus, \"kOuCxF+FOj1AORffougFeEMBivM=\");\n_c = GoogleOAuthStatus;\nexport default GoogleOAuthStatus;\nvar _c;\n$RefreshReg$(_c, \"GoogleOAuthStatus\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Typography", "Link", "Chip", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "GOOGLE_OAUTH_CONFIG", "jsxDEV", "_jsxDEV", "GoogleOAuthStatus", "_s", "status", "setStatus", "message", "setMessage", "checkGoogleOAuthSetup", "clientId", "includes", "getAlertProps", "severity", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "alertProps", "sx", "mb", "children", "borderRadius", "width", "fontWeight", "variant", "mt", "label", "color", "size", "mr", "component", "href", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/components/GoogleOAuthStatus.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { \n  <PERSON>ert, \n  AlertTitle, \n  Box, \n  Typography, \n  Link,\n  Chip\n} from '@mui/material';\nimport { \n  CheckCircle as CheckIcon, \n  Warning as WarningIcon,\n  Info as InfoIcon \n} from '@mui/icons-material';\nimport { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';\n\nconst GoogleOAuthStatus = () => {\n  const [status, setStatus] = useState('checking');\n  const [message, setMessage] = useState('');\n\n  useEffect(() => {\n    checkGoogleOAuthSetup();\n  }, []);\n\n  const checkGoogleOAuthSetup = () => {\n    const clientId = GOOGLE_OAUTH_CONFIG.clientId;\n    \n    if (!clientId || clientId === 'YOUR_GOOGLE_CLIENT_ID_HERE') {\n      setStatus('not-configured');\n      setMessage('Google OAuth is not configured. Please follow the setup guide.');\n    } else if (clientId.includes('your_google_client_id_here')) {\n      setStatus('not-configured');\n      setMessage('Please replace the placeholder with your actual Google Client ID.');\n    } else {\n      setStatus('configured');\n      setMessage('Google OAuth is properly configured and ready to use.');\n    }\n  };\n\n  const getAlertProps = () => {\n    switch (status) {\n      case 'configured':\n        return {\n          severity: 'success',\n          icon: <CheckIcon />,\n          title: 'Google OAuth Ready'\n        };\n      case 'not-configured':\n        return {\n          severity: 'warning',\n          icon: <WarningIcon />,\n          title: 'Setup Required'\n        };\n      default:\n        return {\n          severity: 'info',\n          icon: <InfoIcon />,\n          title: 'Checking Configuration'\n        };\n    }\n  };\n\n  const alertProps = getAlertProps();\n\n  return (\n    <Box sx={{ mb: 2 }}>\n      <Alert \n        severity={alertProps.severity} \n        icon={alertProps.icon}\n        sx={{ \n          borderRadius: 2,\n          '& .MuiAlert-message': {\n            width: '100%'\n          }\n        }}\n      >\n        <AlertTitle sx={{ fontWeight: 600 }}>\n          {alertProps.title}\n        </AlertTitle>\n        \n        <Typography variant=\"body2\" sx={{ mb: 1 }}>\n          {message}\n        </Typography>\n\n        {status === 'configured' && (\n          <Box sx={{ mt: 1 }}>\n            <Chip \n              label=\"OAuth Ready\" \n              color=\"success\" \n              size=\"small\" \n              sx={{ mr: 1 }}\n            />\n            <Chip \n              label=\"Responsive Design\" \n              color=\"primary\" \n              size=\"small\" \n              sx={{ mr: 1 }}\n            />\n            <Chip \n              label=\"Mobile Optimized\" \n              color=\"secondary\" \n              size=\"small\" \n            />\n          </Box>\n        )}\n\n        {status === 'not-configured' && (\n          <Box sx={{ mt: 1 }}>\n            <Typography variant=\"body2\" sx={{ mb: 1 }}>\n              To enable Google OAuth:\n            </Typography>\n            <Typography variant=\"body2\" component=\"div\">\n              1. Follow the setup guide in{' '}\n              <Link \n                href=\"/GOOGLE_OAUTH_SETUP.md\" \n                target=\"_blank\"\n                sx={{ fontWeight: 600 }}\n              >\n                GOOGLE_OAUTH_SETUP.md\n              </Link>\n              <br />\n              2. Update your <code>.env</code> file with the Google Client ID\n              <br />\n              3. Restart the development server\n            </Typography>\n          </Box>\n        )}\n      </Alert>\n    </Box>\n  );\n};\n\nexport default GoogleOAuthStatus;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SACEC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,QACC,eAAe;AACtB,SACEC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,UAAU,CAAC;EAChD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdsB,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,QAAQ,GAAGV,mBAAmB,CAACU,QAAQ;IAE7C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,KAAK,4BAA4B,EAAE;MAC1DJ,SAAS,CAAC,gBAAgB,CAAC;MAC3BE,UAAU,CAAC,gEAAgE,CAAC;IAC9E,CAAC,MAAM,IAAIE,QAAQ,CAACC,QAAQ,CAAC,4BAA4B,CAAC,EAAE;MAC1DL,SAAS,CAAC,gBAAgB,CAAC;MAC3BE,UAAU,CAAC,mEAAmE,CAAC;IACjF,CAAC,MAAM;MACLF,SAAS,CAAC,YAAY,CAAC;MACvBE,UAAU,CAAC,uDAAuD,CAAC;IACrE;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQP,MAAM;MACZ,KAAK,YAAY;QACf,OAAO;UACLQ,QAAQ,EAAE,SAAS;UACnBC,IAAI,eAAEZ,OAAA,CAACP,SAAS;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACnBC,KAAK,EAAE;QACT,CAAC;MACH,KAAK,gBAAgB;QACnB,OAAO;UACLN,QAAQ,EAAE,SAAS;UACnBC,IAAI,eAAEZ,OAAA,CAACL,WAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACrBC,KAAK,EAAE;QACT,CAAC;MACH;QACE,OAAO;UACLN,QAAQ,EAAE,MAAM;UAChBC,IAAI,eAAEZ,OAAA,CAACH,QAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAClBC,KAAK,EAAE;QACT,CAAC;IACL;EACF,CAAC;EAED,MAAMC,UAAU,GAAGR,aAAa,CAAC,CAAC;EAElC,oBACEV,OAAA,CAACZ,GAAG;IAAC+B,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACjBrB,OAAA,CAACd,KAAK;MACJyB,QAAQ,EAAEO,UAAU,CAACP,QAAS;MAC9BC,IAAI,EAAEM,UAAU,CAACN,IAAK;MACtBO,EAAE,EAAE;QACFG,YAAY,EAAE,CAAC;QACf,qBAAqB,EAAE;UACrBC,KAAK,EAAE;QACT;MACF,CAAE;MAAAF,QAAA,gBAEFrB,OAAA,CAACb,UAAU;QAACgC,EAAE,EAAE;UAAEK,UAAU,EAAE;QAAI,CAAE;QAAAH,QAAA,EACjCH,UAAU,CAACD;MAAK;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEbhB,OAAA,CAACX,UAAU;QAACoC,OAAO,EAAC,OAAO;QAACN,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EACvChB;MAAO;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEZb,MAAM,KAAK,YAAY,iBACtBH,OAAA,CAACZ,GAAG;QAAC+B,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACjBrB,OAAA,CAACT,IAAI;UACHoC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,OAAO;UACZV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFhB,OAAA,CAACT,IAAI;UACHoC,KAAK,EAAC,mBAAmB;UACzBC,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,OAAO;UACZV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFhB,OAAA,CAACT,IAAI;UACHoC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAC,WAAW;UACjBC,IAAI,EAAC;QAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAEAb,MAAM,KAAK,gBAAgB,iBAC1BH,OAAA,CAACZ,GAAG;QAAC+B,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACjBrB,OAAA,CAACX,UAAU;UAACoC,OAAO,EAAC,OAAO;UAACN,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,EAAC;QAE3C;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhB,OAAA,CAACX,UAAU;UAACoC,OAAO,EAAC,OAAO;UAACM,SAAS,EAAC,KAAK;UAAAV,QAAA,GAAC,8BACd,EAAC,GAAG,eAChCrB,OAAA,CAACV,IAAI;YACH0C,IAAI,EAAC,wBAAwB;YAC7BC,MAAM,EAAC,QAAQ;YACfd,EAAE,EAAE;cAAEK,UAAU,EAAE;YAAI,CAAE;YAAAH,QAAA,EACzB;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPhB,OAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,mBACS,eAAAhB,OAAA;YAAAqB,QAAA,EAAM;UAAI;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,mCAChC,eAAAhB,OAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,qCAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACd,EAAA,CAlHID,iBAAiB;AAAAiC,EAAA,GAAjBjC,iBAAiB;AAoHvB,eAAeA,iBAAiB;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}