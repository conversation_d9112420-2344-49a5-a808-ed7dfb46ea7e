{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\App.js\";\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport Login from './pages/Login';\nimport Signup from './pages/Signup';\nimport Profile from './pages/Profile';\nimport ForgotPassword from './pages/ForgotPassword';\nimport PrivateRoute from './components/PrivateRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0'\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#ff5983',\n      dark: '#9a0036'\n    },\n    background: {\n      default: '#f5f5f5',\n      paper: '#ffffff'\n    },\n    text: {\n      primary: '#212121',\n      secondary: '#757575'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h4: {\n      fontWeight: 600,\n      fontSize: '1.75rem',\n      lineHeight: 1.2\n    },\n    h5: {\n      fontWeight: 500,\n      fontSize: '1.25rem'\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.5\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.43\n    },\n    button: {\n      textTransform: 'none',\n      fontWeight: 500\n    }\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 8,\n          padding: '10px 24px',\n          fontSize: '0.875rem',\n          fontWeight: 500\n        },\n        sizeLarge: {\n          padding: '12px 24px',\n          fontSize: '1rem'\n        }\n      }\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 8\n          }\n        }\n      }\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12\n        }\n      }\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/signup\",\n          element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/forgot-password\",\n          element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/profile\",\n          element: /*#__PURE__*/_jsxDEV(PrivateRoute, {\n            children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "<PERSON><PERSON>", "Signup", "Profile", "ForgotPassword", "PrivateRoute", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "h4", "fontWeight", "fontSize", "lineHeight", "h5", "body1", "body2", "button", "textTransform", "shape", "borderRadius", "components", "MuiB<PERSON>on", "styleOverrides", "root", "padding", "sizeLarge", "MuiTextField", "MuiPaper", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { ThemeProvider, createTheme } from '@mui/material';\r\nimport CssBaseline from '@mui/material/CssBaseline';\r\nimport Login from './pages/Login';\r\nimport Signup from './pages/Signup';\r\nimport Profile from './pages/Profile';\r\nimport ForgotPassword from './pages/ForgotPassword';\r\nimport PrivateRoute from './components/PrivateRoute';\r\n\r\nconst theme = createTheme({\r\n  palette: {\r\n    primary: {\r\n      main: '#1976d2',\r\n      light: '#42a5f5',\r\n      dark: '#1565c0',\r\n    },\r\n    secondary: {\r\n      main: '#dc004e',\r\n      light: '#ff5983',\r\n      dark: '#9a0036',\r\n    },\r\n    background: {\r\n      default: '#f5f5f5',\r\n      paper: '#ffffff',\r\n    },\r\n    text: {\r\n      primary: '#212121',\r\n      secondary: '#757575',\r\n    },\r\n  },\r\n  typography: {\r\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\r\n    h4: {\r\n      fontWeight: 600,\r\n      fontSize: '1.75rem',\r\n      lineHeight: 1.2,\r\n    },\r\n    h5: {\r\n      fontWeight: 500,\r\n      fontSize: '1.25rem',\r\n    },\r\n    body1: {\r\n      fontSize: '1rem',\r\n      lineHeight: 1.5,\r\n    },\r\n    body2: {\r\n      fontSize: '0.875rem',\r\n      lineHeight: 1.43,\r\n    },\r\n    button: {\r\n      textTransform: 'none',\r\n      fontWeight: 500,\r\n    },\r\n  },\r\n  shape: {\r\n    borderRadius: 8,\r\n  },\r\n  components: {\r\n    MuiButton: {\r\n      styleOverrides: {\r\n        root: {\r\n          borderRadius: 8,\r\n          padding: '10px 24px',\r\n          fontSize: '0.875rem',\r\n          fontWeight: 500,\r\n        },\r\n        sizeLarge: {\r\n          padding: '12px 24px',\r\n          fontSize: '1rem',\r\n        },\r\n      },\r\n    },\r\n    MuiTextField: {\r\n      styleOverrides: {\r\n        root: {\r\n          '& .MuiOutlinedInput-root': {\r\n            borderRadius: 8,\r\n          },\r\n        },\r\n      },\r\n    },\r\n    MuiPaper: {\r\n      styleOverrides: {\r\n        root: {\r\n          borderRadius: 12,\r\n        },\r\n      },\r\n    },\r\n  },\r\n});\r\n\r\nfunction App() {\r\n  return (\r\n    <ThemeProvider theme={theme}>\r\n      <CssBaseline />\r\n      <Router>\r\n        <Routes>\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/signup\" element={<Signup />} />\r\n          <Route path=\"/forgot-password\" element={<ForgotPassword />} />\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <PrivateRoute>\r\n                <Profile />\r\n              </PrivateRoute>\r\n            }\r\n          />\r\n          <Route path=\"/\" element={<Navigate to=\"/login\" replace />} />\r\n        </Routes>\r\n      </Router>\r\n    </ThemeProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,aAAa,EAAEC,WAAW,QAAQ,eAAe;AAC1D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,YAAY,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,KAAK,GAAGT,WAAW,CAAC;EACxBU,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAClBI,SAAS,EAAE;IACb;EACF,CAAC;EACDK,UAAU,EAAE;IACVC,UAAU,EAAE,4CAA4C;IACxDC,EAAE,EAAE;MACFC,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFH,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDG,KAAK,EAAE;MACLH,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;IACd,CAAC;IACDG,KAAK,EAAE;MACLJ,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE;IACd,CAAC;IACDI,MAAM,EAAE;MACNC,aAAa,EAAE,MAAM;MACrBP,UAAU,EAAE;IACd;EACF,CAAC;EACDQ,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJJ,YAAY,EAAE,CAAC;UACfK,OAAO,EAAE,WAAW;UACpBb,QAAQ,EAAE,UAAU;UACpBD,UAAU,EAAE;QACd,CAAC;QACDe,SAAS,EAAE;UACTD,OAAO,EAAE,WAAW;UACpBb,QAAQ,EAAE;QACZ;MACF;IACF,CAAC;IACDe,YAAY,EAAE;MACZJ,cAAc,EAAE;QACdC,IAAI,EAAE;UACJ,0BAA0B,EAAE;YAC1BJ,YAAY,EAAE;UAChB;QACF;MACF;IACF,CAAC;IACDQ,QAAQ,EAAE;MACRL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJJ,YAAY,EAAE;QAChB;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,SAASS,GAAGA,CAAA,EAAG;EACb,oBACEjC,OAAA,CAACT,aAAa;IAACU,KAAK,EAAEA,KAAM;IAAAiC,QAAA,gBAC1BlC,OAAA,CAACP,WAAW;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACftC,OAAA,CAACb,MAAM;MAAA+C,QAAA,eACLlC,OAAA,CAACZ,MAAM;QAAA8C,QAAA,gBACLlC,OAAA,CAACX,KAAK;UAACkD,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAExC,OAAA,CAACN,KAAK;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CtC,OAAA,CAACX,KAAK;UAACkD,IAAI,EAAC,SAAS;UAACC,OAAO,eAAExC,OAAA,CAACL,MAAM;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CtC,OAAA,CAACX,KAAK;UAACkD,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAExC,OAAA,CAACH,cAAc;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DtC,OAAA,CAACX,KAAK;UACJkD,IAAI,EAAC,UAAU;UACfC,OAAO,eACLxC,OAAA,CAACF,YAAY;YAAAoC,QAAA,eACXlC,OAAA,CAACJ,OAAO;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFtC,OAAA,CAACX,KAAK;UAACkD,IAAI,EAAC,GAAG;UAACC,OAAO,eAAExC,OAAA,CAACV,QAAQ;YAACmD,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACK,EAAA,GAtBQV,GAAG;AAwBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}