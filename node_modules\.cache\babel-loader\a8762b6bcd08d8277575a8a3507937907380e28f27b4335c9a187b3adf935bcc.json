{"ast": null, "code": "// Google OAuth Configuration\n// To set up Google OAuth, follow these steps:\n\n// 1. Go to Google Cloud Console (https://console.cloud.google.com/)\n// 2. Create a new project or select existing project\n// 3. Enable Google+ API and Google OAuth2 API\n// 4. Go to \"Credentials\" section\n// 5. Create OAuth 2.0 Client ID\n// 6. Add your domain to authorized origins:\n//    - http://localhost:3000 (for development)\n//    - Your production domain\n// 7. Add redirect URIs:\n//    - http://localhost:3000 (for development)\n//    - Your production domain\n// 8. Copy the Client ID and replace the placeholder below\n\nexport const GOOGLE_OAUTH_CONFIG = {\n  // Replace this with your actual Google OAuth Client ID\n  clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID || \"YOUR_GOOGLE_CLIENT_ID_HERE\",\n  // OAuth scopes - what information we want to access\n  scope: \"profile email\",\n  // Response type\n  responseType: \"code\",\n  // Access type\n  accessType: \"offline\",\n  // Prompt\n  prompt: \"consent\"\n};\n\n// Environment setup instructions:\n// Create a .env file in your project root and add:\n// REACT_APP_GOOGLE_CLIENT_ID=your_actual_client_id_here\n\nexport default GOOGLE_OAUTH_CONFIG;", "map": {"version": 3, "names": ["GOOGLE_OAUTH_CONFIG", "clientId", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "scope", "responseType", "accessType", "prompt"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/config/googleOAuth.js"], "sourcesContent": ["// Google OAuth Configuration\n// To set up Google OAuth, follow these steps:\n\n// 1. Go to Google Cloud Console (https://console.cloud.google.com/)\n// 2. Create a new project or select existing project\n// 3. Enable Google+ API and Google OAuth2 API\n// 4. Go to \"Credentials\" section\n// 5. Create OAuth 2.0 Client ID\n// 6. Add your domain to authorized origins:\n//    - http://localhost:3000 (for development)\n//    - Your production domain\n// 7. Add redirect URIs:\n//    - http://localhost:3000 (for development)\n//    - Your production domain\n// 8. Copy the Client ID and replace the placeholder below\n\nexport const GOOGLE_OAUTH_CONFIG = {\n  // Replace this with your actual Google OAuth Client ID\n  clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID || \"YOUR_GOOGLE_CLIENT_ID_HERE\",\n  \n  // OAuth scopes - what information we want to access\n  scope: \"profile email\",\n  \n  // Response type\n  responseType: \"code\",\n  \n  // Access type\n  accessType: \"offline\",\n  \n  // Prompt\n  prompt: \"consent\"\n};\n\n// Environment setup instructions:\n// Create a .env file in your project root and add:\n// REACT_APP_GOOGLE_CLIENT_ID=your_actual_client_id_here\n\nexport default GOOGLE_OAUTH_CONFIG;\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,mBAAmB,GAAG;EACjC;EACAC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAI,4BAA4B;EAEhF;EACAC,KAAK,EAAE,eAAe;EAEtB;EACAC,YAAY,EAAE,MAAM;EAEpB;EACAC,UAAU,EAAE,SAAS;EAErB;EACAC,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;;AAEA,eAAeR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}