// Google OAuth Configuration
// To set up Google OAuth, follow these steps:

// 1. Go to Google Cloud Console (https://console.cloud.google.com/)
// 2. Create a new project or select existing project
// 3. Enable Google+ API and Google OAuth2 API
// 4. Go to "Credentials" section
// 5. Create OAuth 2.0 Client ID
// 6. Add your domain to authorized origins:
//    - http://localhost:3000 (for development)
//    - Your production domain
// 7. Add redirect URIs:
//    - http://localhost:3000 (for development)
//    - Your production domain
// 8. Copy the Client ID and replace the placeholder below

export const GOOGLE_OAUTH_CONFIG = {
  // Replace this with your actual Google OAuth Client ID
  clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID || "YOUR_GOOGLE_CLIENT_ID_HERE",
  
  // OAuth scopes - what information we want to access
  scope: "profile email",
  
  // Response type
  responseType: "code",
  
  // Access type
  accessType: "offline",
  
  // Prompt
  prompt: "consent"
};

// Environment setup instructions:
// Create a .env file in your project root and add:
// REACT_APP_GOOGLE_CLIENT_ID=your_actual_client_id_here

export default GOOGLE_OAUTH_CONFIG;
