# Houzer Frontend Integration Guide

## Overview
This guide provides implementation details for integrating the Houzer-style authentication API with the React.js frontend application.

## Enhanced API Service Layer

### 1. <PERSON>uzer Auth Service

```typescript
// src/api/houzerAuthService.ts
import axios, { AxiosResponse } from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';

// Create axios instance with custom headers
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add custom MIME types for Houzer API
    if (config.url?.includes('/verification/send-otp')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.verification.send-otp.req-v1+json';
    } else if (config.url?.includes('/verification/verify-otp')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.verification.verify-otp.req-v1+json';
    } else if (config.url?.includes('/verification/resend-otp')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.verification.resend-otp.req-v1+json';
    } else if (config.url?.includes('/signup')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.signup.req-v1+json';
    } else if (config.url?.includes('/login')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.login.req-v1+json';
    } else if (config.url?.includes('/forgot-password')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.forgot-password.req-v1+json';
    } else if (config.url?.includes('/reset-password')) {
      config.headers['Content-Type'] = 'application/vnd.chidhagni-houzer.reset-password.req-v1+json';
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
            refreshToken
          });

          const { accessToken } = response.data.data;
          localStorage.setItem('accessToken', accessToken);

          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

export const houzerAuthService = {
  // OTP Verification Methods
  sendOTP: async (contactType: string, contactValue: string): Promise<any> => {
    const ipAddress = await getClientIP();
    
    const response: AxiosResponse<HouzerApiResponse<any>> = await apiClient.post(
      '/public/api/v1/verification/send-otp',
      {
        contactType,
        contactValue,
        ipAddress
      }
    );

    return response.data.data;
  },

  verifyOTP: async (contactType: string, contactValue: string, otp: string): Promise<any> => {
    const response: AxiosResponse<HouzerApiResponse<any>> = await apiClient.post(
      '/public/api/v1/verification/verify-otp',
      {
        contactType,
        contactValue,
        otp
      }
    );

    return response.data.data;
  },

  resendOTP: async (contactType: string, contactValue: string): Promise<any> => {
    const response: AxiosResponse<HouzerApiResponse<any>> = await apiClient.post(
      '/public/api/v1/verification/resend-otp',
      {
        contactType,
        contactValue
      }
    );

    return response.data.data;
  },

  // Authentication Methods
  signup: async (signupData: SignupRequestDTO): Promise<SignupResponseDTO> => {
    const response: AxiosResponse<HouzerApiResponse<SignupResponseDTO>> = await apiClient.post(
      '/public/api/v1/signup',
      signupData
    );

    return response.data.data;
  },

  googleSignup: async (googleSignupData: GoogleSignupRequestDTO): Promise<any> => {
    const response: AxiosResponse<HouzerApiResponse<any>> = await apiClient.post(
      '/public/api/v1/auth/google-signup',
      googleSignupData
    );

    return response.data.data;
  },

  login: async (loginData: LoginRequestDTO): Promise<LoginResponseDTO> => {
    const ipAddress = await getClientIP();
    const deviceInfo = getDeviceInfo();

    const response: AxiosResponse<HouzerApiResponse<LoginResponseDTO>> = await apiClient.post(
      '/api/v1/login',
      {
        ...loginData,
        deviceInfo,
        ipAddress
      }
    );

    const { individual, organization, tokens } = response.data.data;

    // Store tokens and user data
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
    localStorage.setItem('individual', JSON.stringify(individual));
    localStorage.setItem('organization', JSON.stringify(organization));

    return response.data.data;
  },

  forgotPassword: async (email: string): Promise<any> => {
    const ipAddress = await getClientIP();
    
    const response: AxiosResponse<HouzerApiResponse<any>> = await apiClient.post(
      '/public/api/v1/forgot-password',
      {
        email,
        ipAddress
      }
    );

    return response.data.data;
  },

  resetPassword: async (resetData: ResetPasswordRequestDTO): Promise<any> => {
    const response: AxiosResponse<HouzerApiResponse<any>> = await apiClient.post(
      '/public/api/v1/reset-password',
      resetData
    );

    return response.data.data;
  },

  // Utility Methods
  logout: async (): Promise<void> => {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        await apiClient.post('/api/v1/logout', { refreshToken });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('individual');
      localStorage.removeItem('organization');
    }
  },

  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('accessToken');
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  },

  getCurrentIndividual: (): any | null => {
    const individualStr = localStorage.getItem('individual');
    return individualStr ? JSON.parse(individualStr) : null;
  },

  getCurrentOrganization: (): any | null => {
    const organizationStr = localStorage.getItem('organization');
    return organizationStr ? JSON.parse(organizationStr) : null;
  }
};

// Utility functions
const getClientIP = async (): Promise<string> => {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch {
    return '0.0.0.0';
  }
};

const getDeviceInfo = () => {
  const userAgent = navigator.userAgent;
  let deviceName = 'Unknown Device';
  let platform = 'Web';

  if (/iPhone/.test(userAgent)) {
    deviceName = 'iPhone';
    platform = 'iOS';
  } else if (/iPad/.test(userAgent)) {
    deviceName = 'iPad';
    platform = 'iOS';
  } else if (/Android/.test(userAgent)) {
    deviceName = 'Android Device';
    platform = 'Android';
  } else if (/Windows/.test(userAgent)) {
    deviceName = 'Windows PC';
    platform = 'Windows';
  } else if (/Mac/.test(userAgent)) {
    deviceName = 'Mac';
    platform = 'macOS';
  }

  return {
    deviceId: getOrCreateDeviceId(),
    deviceName,
    platform,
    appVersion: process.env.REACT_APP_VERSION || '1.0.0'
  };
};

const getOrCreateDeviceId = (): string => {
  let deviceId = localStorage.getItem('deviceId');
  if (!deviceId) {
    deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('deviceId', deviceId);
  }
  return deviceId;
};

// Type definitions
interface HouzerApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  requestId: string;
}

interface SignupRequestDTO {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  organizationName: string;
  organizationType: string;
  mobileNumber: string;
  verificationToken: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}

interface SignupResponseDTO {
  individualId: string;
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  organizationType: string;
  status: string;
  emailVerified: boolean;
  mobileVerified: boolean;
  message: string;
}

interface GoogleSignupRequestDTO {
  googleToken: string;
  organizationName: string;
  organizationType: string;
  mobileNumber: string;
  acceptTerms: boolean;
  marketingConsent?: boolean;
}

interface LoginRequestDTO {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface LoginResponseDTO {
  individual: any;
  organization: any;
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
  };
  lastLoginAt: string;
  message: string;
}

interface ResetPasswordRequestDTO {
  resetToken: string;
  newPassword: string;
  confirmPassword: string;
}

export default houzerAuthService;
```

### 2. OTP Verification Component

```typescript
// src/components/OTPVerification.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Link
} from '@mui/material';
import { houzerAuthService } from '../api/houzerAuthService';

interface OTPVerificationProps {
  contactType: 'EMAIL' | 'MOBILE';
  contactValue: string;
  onVerificationSuccess: (verificationToken: string) => void;
  onBack: () => void;
}

const OTPVerification: React.FC<OTPVerificationProps> = ({
  contactType,
  contactValue,
  onVerificationSuccess,
  onBack
}) => {
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleVerifyOTP = async () => {
    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await houzerAuthService.verifyOTP(contactType, contactValue, otp);
      setSuccess('OTP verified successfully!');
      onVerificationSuccess(response.verificationToken);
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'OTP verification failed';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setLoading(true);
    setError('');
    setCanResend(false);
    setTimeLeft(300);

    try {
      await houzerAuthService.resendOTP(contactType, contactValue);
      setSuccess('OTP resent successfully!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Failed to resend OTP';
      setError(errorMessage);
      setCanResend(true);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Box sx={{ maxWidth: 400, mx: 'auto', p: 3 }}>
      <Typography variant="h5" gutterBottom align="center">
        Verify OTP
      </Typography>
      
      <Typography variant="body2" color="textSecondary" align="center" sx={{ mb: 3 }}>
        We've sent a 6-digit code to {contactValue}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <TextField
        fullWidth
        label="Enter OTP"
        value={otp}
        onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
        placeholder="000000"
        inputProps={{
          maxLength: 6,
          style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
        }}
        sx={{ mb: 3 }}
      />

      <Button
        fullWidth
        variant="contained"
        onClick={handleVerifyOTP}
        disabled={loading || otp.length !== 6}
        sx={{ mb: 2 }}
      >
        {loading ? <CircularProgress size={24} /> : 'Verify OTP'}
      </Button>

      <Box sx={{ textAlign: 'center', mb: 2 }}>
        {timeLeft > 0 ? (
          <Typography variant="body2" color="textSecondary">
            Resend OTP in {formatTime(timeLeft)}
          </Typography>
        ) : (
          <Link
            component="button"
            variant="body2"
            onClick={handleResendOTP}
            disabled={loading || !canResend}
          >
            Resend OTP
          </Link>
        )}
      </Box>

      <Button
        fullWidth
        variant="outlined"
        onClick={onBack}
        disabled={loading}
      >
        Back
      </Button>
    </Box>
  );
};

export default OTPVerification;
```
