{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\components\\\\GoogleOAuthButton.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { Button, CircularProgress, Box, Typography } from '@mui/material';\nimport { Google as GoogleIcon } from '@mui/icons-material';\nimport googleAuthService from '../services/googleAuthService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GoogleOAuthButton = ({\n  onSuccess,\n  onError,\n  disabled = false,\n  variant = \"outlined\",\n  size = \"large\",\n  fullWidth = true,\n  text = \"Continue with Google\"\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [isGapiReady, setIsGapiReady] = useState(false);\n  useEffect(() => {\n    // Initialize Google API when component mounts\n    const initializeGoogleAuth = async () => {\n      try {\n        await googleAuthService.initializeGapi();\n        setIsGapiReady(true);\n      } catch (error) {\n        console.error('Failed to initialize Google Auth:', error);\n        if (onError) {\n          onError('Failed to initialize Google authentication');\n        }\n      }\n    };\n    initializeGoogleAuth();\n  }, [onError]);\n  const handleGoogleSignIn = async () => {\n    if (!isGapiReady) {\n      if (onError) {\n        onError('Google authentication is not ready. Please try again.');\n      }\n      return;\n    }\n    setLoading(true);\n    try {\n      const result = await googleAuthService.signIn();\n      if (result.success) {\n        if (onSuccess) {\n          onSuccess(result);\n        }\n      } else {\n        if (onError) {\n          onError(result.error || 'Google sign-in failed');\n        }\n      }\n    } catch (error) {\n      console.error('Google sign-in error:', error);\n      if (onError) {\n        onError('An unexpected error occurred during Google sign-in');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    variant: variant,\n    size: size,\n    fullWidth: fullWidth,\n    disabled: disabled || loading || !isGapiReady,\n    onClick: handleGoogleSignIn,\n    startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(GoogleIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 11\n    }, this),\n    sx: {\n      borderColor: '#dadce0',\n      color: '#3c4043',\n      backgroundColor: '#fff',\n      textTransform: 'none',\n      fontWeight: 500,\n      fontSize: '14px',\n      height: '48px',\n      '&:hover': {\n        backgroundColor: '#f8f9fa',\n        borderColor: '#dadce0',\n        boxShadow: '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)'\n      },\n      '&:focus': {\n        backgroundColor: '#f8f9fa',\n        borderColor: '#4285f4',\n        outline: 'none'\n      },\n      '&:disabled': {\n        backgroundColor: '#f8f9fa',\n        color: '#9aa0a6',\n        borderColor: '#f8f9fa'\n      },\n      boxShadow: '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)',\n      border: '1px solid #dadce0'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: \"Signing in...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(GoogleOAuthButton, \"BO1h6V1yau/akTbcr7t1pFMtM2o=\");\n_c = GoogleOAuthButton;\nexport default GoogleOAuthButton;\nvar _c;\n$RefreshReg$(_c, \"GoogleOAuthButton\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON>", "CircularProgress", "Box", "Typography", "Google", "GoogleIcon", "googleAuthService", "jsxDEV", "_jsxDEV", "GoogleOAuthButton", "onSuccess", "onError", "disabled", "variant", "size", "fullWidth", "text", "_s", "loading", "setLoading", "isGapiReady", "setIsGapiReady", "initializeGoogleAuth", "initializeGapi", "error", "console", "handleGoogleSignIn", "result", "signIn", "success", "onClick", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "borderColor", "color", "backgroundColor", "textTransform", "fontWeight", "fontSize", "height", "boxShadow", "outline", "border", "children", "display", "alignItems", "gap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/components/GoogleOAuthButton.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { Button, CircularProgress, Box, Typography } from '@mui/material';\nimport { Google as GoogleIcon } from '@mui/icons-material';\nimport googleAuthService from '../services/googleAuthService';\n\nconst GoogleOAuthButton = ({ \n  onSuccess, \n  onError, \n  disabled = false, \n  variant = \"outlined\",\n  size = \"large\",\n  fullWidth = true,\n  text = \"Continue with Google\"\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [isGapiReady, setIsGapiReady] = useState(false);\n\n  useEffect(() => {\n    // Initialize Google API when component mounts\n    const initializeGoogleAuth = async () => {\n      try {\n        await googleAuthService.initializeGapi();\n        setIsGapiReady(true);\n      } catch (error) {\n        console.error('Failed to initialize Google Auth:', error);\n        if (onError) {\n          onError('Failed to initialize Google authentication');\n        }\n      }\n    };\n\n    initializeGoogleAuth();\n  }, [onError]);\n\n  const handleGoogleSignIn = async () => {\n    if (!isGapiReady) {\n      if (onError) {\n        onError('Google authentication is not ready. Please try again.');\n      }\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const result = await googleAuthService.signIn();\n      \n      if (result.success) {\n        if (onSuccess) {\n          onSuccess(result);\n        }\n      } else {\n        if (onError) {\n          onError(result.error || 'Google sign-in failed');\n        }\n      }\n    } catch (error) {\n      console.error('Google sign-in error:', error);\n      if (onError) {\n        onError('An unexpected error occurred during Google sign-in');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Button\n      variant={variant}\n      size={size}\n      fullWidth={fullWidth}\n      disabled={disabled || loading || !isGapiReady}\n      onClick={handleGoogleSignIn}\n      startIcon={\n        loading ? (\n          <CircularProgress size={20} />\n        ) : (\n          <GoogleIcon />\n        )\n      }\n      sx={{\n        borderColor: '#dadce0',\n        color: '#3c4043',\n        backgroundColor: '#fff',\n        textTransform: 'none',\n        fontWeight: 500,\n        fontSize: '14px',\n        height: '48px',\n        '&:hover': {\n          backgroundColor: '#f8f9fa',\n          borderColor: '#dadce0',\n          boxShadow: '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)',\n        },\n        '&:focus': {\n          backgroundColor: '#f8f9fa',\n          borderColor: '#4285f4',\n          outline: 'none',\n        },\n        '&:disabled': {\n          backgroundColor: '#f8f9fa',\n          color: '#9aa0a6',\n          borderColor: '#f8f9fa',\n        },\n        boxShadow: '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)',\n        border: '1px solid #dadce0',\n      }}\n    >\n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n        {loading ? (\n          <Typography variant=\"body2\">Signing in...</Typography>\n        ) : (\n          <Typography variant=\"body2\">{text}</Typography>\n        )}\n      </Box>\n    </Button>\n  );\n};\n\nexport default GoogleOAuthButton;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AACzE,SAASC,MAAM,IAAIC,UAAU,QAAQ,qBAAqB;AAC1D,OAAOC,iBAAiB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,SAAS;EACTC,OAAO;EACPC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,UAAU;EACpBC,IAAI,GAAG,OAAO;EACdC,SAAS,GAAG,IAAI;EAChBC,IAAI,GAAG;AACT,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF,MAAMhB,iBAAiB,CAACiB,cAAc,CAAC,CAAC;QACxCF,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAIb,OAAO,EAAE;UACXA,OAAO,CAAC,4CAA4C,CAAC;QACvD;MACF;IACF,CAAC;IAEDW,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;EAEb,MAAMe,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACN,WAAW,EAAE;MAChB,IAAIT,OAAO,EAAE;QACXA,OAAO,CAAC,uDAAuD,CAAC;MAClE;MACA;IACF;IAEAQ,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMQ,MAAM,GAAG,MAAMrB,iBAAiB,CAACsB,MAAM,CAAC,CAAC;MAE/C,IAAID,MAAM,CAACE,OAAO,EAAE;QAClB,IAAInB,SAAS,EAAE;UACbA,SAAS,CAACiB,MAAM,CAAC;QACnB;MACF,CAAC,MAAM;QACL,IAAIhB,OAAO,EAAE;UACXA,OAAO,CAACgB,MAAM,CAACH,KAAK,IAAI,uBAAuB,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAIb,OAAO,EAAE;QACXA,OAAO,CAAC,oDAAoD,CAAC;MAC/D;IACF,CAAC,SAAS;MACRQ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEX,OAAA,CAACR,MAAM;IACLa,OAAO,EAAEA,OAAQ;IACjBC,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEA,SAAU;IACrBH,QAAQ,EAAEA,QAAQ,IAAIM,OAAO,IAAI,CAACE,WAAY;IAC9CU,OAAO,EAAEJ,kBAAmB;IAC5BK,SAAS,EACPb,OAAO,gBACLV,OAAA,CAACP,gBAAgB;MAACa,IAAI,EAAE;IAAG;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAE9B3B,OAAA,CAACH,UAAU;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAEhB;IACDC,EAAE,EAAE;MACFC,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,SAAS;MAChBC,eAAe,EAAE,MAAM;MACvBC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE,MAAM;MACd,SAAS,EAAE;QACTJ,eAAe,EAAE,SAAS;QAC1BF,WAAW,EAAE,SAAS;QACtBO,SAAS,EAAE;MACb,CAAC;MACD,SAAS,EAAE;QACTL,eAAe,EAAE,SAAS;QAC1BF,WAAW,EAAE,SAAS;QACtBQ,OAAO,EAAE;MACX,CAAC;MACD,YAAY,EAAE;QACZN,eAAe,EAAE,SAAS;QAC1BD,KAAK,EAAE,SAAS;QAChBD,WAAW,EAAE;MACf,CAAC;MACDO,SAAS,EAAE,kEAAkE;MAC7EE,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,eAEFvC,OAAA,CAACN,GAAG;MAACkC,EAAE,EAAE;QAAEY,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAH,QAAA,EACxD7B,OAAO,gBACNV,OAAA,CAACL,UAAU;QAACU,OAAO,EAAC,OAAO;QAAAkC,QAAA,EAAC;MAAa;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEtD3B,OAAA,CAACL,UAAU;QAACU,OAAO,EAAC,OAAO;QAAAkC,QAAA,EAAE/B;MAAI;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAC/C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAClB,EAAA,CA/GIR,iBAAiB;AAAA0C,EAAA,GAAjB1C,iBAAiB;AAiHvB,eAAeA,iBAAiB;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}