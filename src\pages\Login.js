import { useState } from "react";
import { useN<PERSON>gate, Link as RouterLink } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import {
  Container,
  Paper,
  Typography,
  Button,
  Link,
  Box,
  CircularProgress,
  Snackbar,
  Alert,
  Grid,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { LockOutlined as LockIcon } from "@mui/icons-material";
import FormControlWrapper from "../components/FormControlWrapper";
import PasswordField from "../components/PasswordField";
import GoogleOAuthButton from "../components/GoogleOAuthButton";
import OrDivider from "../components/OrDivider";
import GoogleOAuthStatus from "../components/GoogleOAuthStatus";
import authService from "../api/authService";

const validationSchema = Yup.object({
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  password: Yup.string().required("Password is required"),
});

const defaultValues = {
  email: "",
  password: "",
};

const Login = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [error, setError] = useState("");
  const [showError, setShowError] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues,
    mode: "onChange",
    resolver: yupResolver(validationSchema),
  });

  const onSubmit = async (values) => {
    setLoading(true);

    const handleSuccess = (data) => {
      console.log("Login successful:", data);
      navigate("/profile");
    };

    const handleError = (errorMessage) => {
      setError(errorMessage);
      setShowError(true);
    };

    try {
      await authService.login(values, handleError, handleSuccess);
    } catch (err) {
      // Error already handled by authService
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (googleData) => {
    setGoogleLoading(true);

    const handleSuccess = (data) => {
      console.log("Google login successful:", data);
      navigate("/profile");
    };

    const handleError = (errorMessage) => {
      setError(errorMessage);
      setShowError(true);
    };

    try {
      await authService.googleLogin(googleData, handleError, handleSuccess);
    } catch (err) {
      // Error already handled by authService
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleGoogleError = (errorMessage) => {
    setError(errorMessage);
    setShowError(true);
  };

  return (
    <Container
      component="main"
      maxWidth="xs"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        py: { xs: 2, sm: 4 }
      }}
    >
      <Paper
        elevation={isMobile ? 0 : 3}
        sx={{
          p: { xs: 3, sm: 4 },
          width: '100%',
          backgroundColor: isMobile ? 'transparent' : 'background.paper',
          boxShadow: isMobile ? 'none' : undefined
        }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box
            sx={{
              mx: 'auto',
              mb: 2,
              width: 48,
              height: 48,
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <LockIcon sx={{ color: 'white', fontSize: 24 }} />
          </Box>
          <Typography
            component="h1"
            variant="h4"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: 1
            }}
          >
            Welcome back
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              fontSize: '14px'
            }}
          >
            Sign in to your account to continue
          </Typography>
        </Box>

        {/* Google OAuth Status */}
        <GoogleOAuthStatus />

        {/* Google OAuth Button */}
        <Box sx={{ mb: 2 }}>
          <GoogleOAuthButton
            onSuccess={handleGoogleSuccess}
            onError={handleGoogleError}
            disabled={loading || googleLoading}
            text="Continue with Google"
          />
        </Box>

        {/* Divider */}
        <OrDivider />

        {/* Email/Password Form */}
        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControlWrapper
                name="email"
                control={control}
                rules={{ required: "Email is required" }}
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                error={errors.email}
                helperText={errors.email?.message}
              />
            </Grid>
            <Grid item xs={12}>
              <PasswordField
                name="password"
                control={control}
                rules={{ required: "Password is required" }}
                label="Password"
                placeholder="Enter your password"
                error={errors.password}
                helperText={errors.password?.message}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{
                  mt: 1,
                  mb: 2,
                  height: 48,
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '16px'
                }}
                disabled={loading || googleLoading}
              >
                {loading ? (
                  <>
                    <CircularProgress
                      size={20}
                      sx={{ color: "common.white", mr: 1 }}
                    />
                    Signing in...
                  </>
                ) : (
                  "Sign in"
                )}
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Footer Links */}
        <Box sx={{ textAlign: "center", mt: 3 }}>
          <Link
            component={RouterLink}
            to="/forgot-password"
            variant="body2"
            sx={{
              color: 'primary.main',
              textDecoration: 'none',
              fontWeight: 500,
              '&:hover': {
                textDecoration: 'underline'
              }
            }}
          >
            Forgot password?
          </Link>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Don't have an account?{' '}
              <Link
                component={RouterLink}
                to="/signup"
                sx={{
                  color: 'primary.main',
                  textDecoration: 'none',
                  fontWeight: 600,
                  '&:hover': {
                    textDecoration: 'underline'
                  }
                }}
              >
                Sign up
              </Link>
            </Typography>
          </Box>
        </Box>
      </Paper>

      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity="error"
          onClose={() => setShowError(false)}
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Login;
