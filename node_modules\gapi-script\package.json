{"name": "gapi-script", "version": "1.2.0", "description": "package to load gapi script and some functions", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/partnerhero/gapi-script.git"}, "keywords": ["google", "api", "<PERSON>i", "gapi-script", "google-auth2", "gapi-auth", "google-sign-in"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/partnerhero/gapi-script/issues"}, "homepage": "https://github.com/partnerhero/gapi-script#readme", "types": "./index.d.ts", "devDependencies": {"@types/gapi.auth2": "^0.0.53"}}