{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\sample-auth-app\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { gapi } from 'gapi-script';\nimport App from './App';\nimport { GOOGLE_OAUTH_CONFIG } from './config/googleOAuth';\n\n// Initialize Google API when the app starts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initializeGapi = async () => {\n  try {\n    await gapi.load('auth2', () => {\n      gapi.auth2.init({\n        client_id: GOOGLE_OAUTH_CONFIG.clientId\n      });\n    });\n  } catch (error) {\n    console.error('Error initializing Google API:', error);\n  }\n};\n\n// Initialize Google API\ninitializeGapi();\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 25,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON>i", "App", "GOOGLE_OAUTH_CONFIG", "jsxDEV", "_jsxDEV", "initializeGapi", "load", "auth2", "init", "client_id", "clientId", "error", "console", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Documents/augment-projects/sample-auth-app/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport { gapi } from 'gapi-script';\r\nimport App from './App';\r\nimport { GOOGLE_OAUTH_CONFIG } from './config/googleOAuth';\r\n\r\n// Initialize Google API when the app starts\r\nconst initializeGapi = async () => {\r\n  try {\r\n    await gapi.load('auth2', () => {\r\n      gapi.auth2.init({\r\n        client_id: GOOGLE_OAUTH_CONFIG.clientId,\r\n      });\r\n    });\r\n  } catch (error) {\r\n    console.error('Error initializing Google API:', error);\r\n  }\r\n};\r\n\r\n// Initialize Google API\r\ninitializeGapi();\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\nroot.render(\r\n  <React.StrictMode>\r\n    <App />\r\n  </React.StrictMode>\r\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,IAAI,QAAQ,aAAa;AAClC,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,mBAAmB,QAAQ,sBAAsB;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;EACjC,IAAI;IACF,MAAML,IAAI,CAACM,IAAI,CAAC,OAAO,EAAE,MAAM;MAC7BN,IAAI,CAACO,KAAK,CAACC,IAAI,CAAC;QACdC,SAAS,EAAEP,mBAAmB,CAACQ;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;EACxD;AACF,CAAC;;AAED;AACAN,cAAc,CAAC,CAAC;AAEhB,MAAMQ,IAAI,GAAGd,QAAQ,CAACe,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTb,OAAA,CAACN,KAAK,CAACoB,UAAU;EAAAC,QAAA,eACff,OAAA,CAACH,GAAG;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}