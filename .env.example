# Google OAuth Configuration
# Replace with your actual Google OAuth Client ID from Google Cloud Console
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id_here

# Instructions to set up Google OAuth:
# 1. Go to https://console.cloud.google.com/
# 2. Create a new project or select an existing one
# 3. Enable the Google+ API and Google OAuth2 API
# 4. Go to "Credentials" section
# 5. Create OAuth 2.0 Client ID
# 6. Add authorized origins:
#    - http://localhost:3000 (for development)
#    - Your production domain
# 7. Add redirect URIs:
#    - http://localhost:3000 (for development)
#    - Your production domain
# 8. Copy the Client ID and replace the value above
# 9. Rename this file to .env
