import { gapi } from 'gapi-script';
import { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';

class GoogleAuthService {
  constructor() {
    this.isInitialized = false;
    this.authInstance = null;
  }

  // Initialize Google API
  async initializeGapi() {
    if (this.isInitialized) {
      return this.authInstance;
    }

    try {
      await new Promise((resolve, reject) => {
        gapi.load('auth2', {
          callback: resolve,
          onerror: reject,
        });
      });

      this.authInstance = await gapi.auth2.init({
        client_id: GOOGLE_OAUTH_CONFIG.clientId,
        scope: GOOGLE_OAUTH_CONFIG.scope,
      });

      this.isInitialized = true;
      return this.authInstance;
    } catch (error) {
      console.error('Error initializing Google API:', error);
      throw new Error('Failed to initialize Google authentication');
    }
  }

  // Sign in with Google
  async signIn() {
    try {
      if (!this.isInitialized) {
        await this.initializeGapi();
      }

      const authResult = await this.authInstance.signIn();
      const profile = authResult.getBasicProfile();
      const authResponse = authResult.getAuthResponse();

      return {
        success: true,
        user: {
          id: profile.getId(),
          name: profile.getName(),
          email: profile.getEmail(),
          imageUrl: profile.getImageUrl(),
        },
        token: authResponse.id_token,
        accessToken: authResponse.access_token,
      };
    } catch (error) {
      console.error('Google sign-in error:', error);
      return {
        success: false,
        error: error.error || 'Google sign-in failed',
      };
    }
  }

  // Sign out
  async signOut() {
    try {
      if (this.authInstance) {
        await this.authInstance.signOut();
      }
      return { success: true };
    } catch (error) {
      console.error('Google sign-out error:', error);
      return { success: false, error: 'Sign-out failed' };
    }
  }

  // Check if user is signed in
  isSignedIn() {
    if (!this.authInstance) {
      return false;
    }
    return this.authInstance.isSignedIn.get();
  }

  // Get current user
  getCurrentUser() {
    if (!this.authInstance || !this.isSignedIn()) {
      return null;
    }

    const user = this.authInstance.currentUser.get();
    const profile = user.getBasicProfile();
    
    return {
      id: profile.getId(),
      name: profile.getName(),
      email: profile.getEmail(),
      imageUrl: profile.getImageUrl(),
    };
  }
}

// Create and export a singleton instance
const googleAuthService = new GoogleAuthService();
export default googleAuthService;
