import axios from 'axios';

const API_BASE_URL = 'http://localhost:8080/api/v1';

// Helper function to get authorization headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

const authService = {
  login: async (credentials, errorCallback, successCallback) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/login`, credentials, {
        headers: getAuthHeaders()
      });

      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
      }

      if (successCallback) {
        successCallback(response.data);
      }

      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      if (errorCallback) {
        errorCallback(errorMessage);
      }
      throw errorMessage;
    }
  },

  // Google OAuth login
  googleLogin: async (googleData, errorCallback, successCallback) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/google`, {
        token: googleData.token,
        user: googleData.user
      }, {
        headers: getAuthHeaders()
      });

      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
        // Store user info for quick access
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }

      if (successCallback) {
        successCallback(response.data);
      }

      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Google login failed';
      if (errorCallback) {
        errorCallback(errorMessage);
      }
      throw errorMessage;
    }
  },

  signup: async (userData, errorCallback, successCallback) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/signup`, userData, {
        headers: getAuthHeaders()
      });

      if (successCallback) {
        successCallback(response.data);
      }

      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Signup failed';
      if (errorCallback) {
        errorCallback(errorMessage);
      }
      throw errorMessage;
    }
  },

  forgotPassword: async (email, errorCallback, successCallback) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/forgot-password`, { email }, {
        headers: getAuthHeaders()
      });

      if (successCallback) {
        successCallback(response.data);
      }

      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Password reset failed';
      if (errorCallback) {
        errorCallback(errorMessage);
      }
      throw errorMessage;
    }
  },

  getProfile: async (errorCallback, successCallback) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/profile`, {
        headers: getAuthHeaders()
      });

      if (successCallback) {
        successCallback(response.data);
      }

      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch profile';
      if (errorCallback) {
        errorCallback(errorMessage);
      }
      throw errorMessage;
    }
  },

  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  // Get stored user info
  getStoredUser: () => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
};

export default authService; 