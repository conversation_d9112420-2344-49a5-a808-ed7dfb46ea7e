[{"C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\Login.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\Profile.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\Signup.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\ForgotPassword.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\PrivateRoute.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\api\\authService.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\PasswordField.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\FormControlWrapper.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\OrDivider.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\GoogleOAuthButton.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\services\\googleAuthService.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\config\\googleOAuth.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\GoogleOAuthStatus.js": "15"}, {"size": 704, "mtime": 1750067160428, "results": "16", "hashOfConfig": "17"}, {"size": 2636, "mtime": 1750067133421, "results": "18", "hashOfConfig": "17"}, {"size": 8284, "mtime": 1750068421859, "results": "19", "hashOfConfig": "17"}, {"size": 2930, "mtime": 1749897397847, "results": "20", "hashOfConfig": "17"}, {"size": 8938, "mtime": 1750067068406, "results": "21", "hashOfConfig": "17"}, {"size": 4410, "mtime": 1749897371678, "results": "22", "hashOfConfig": "17"}, {"size": 299, "mtime": 1749894935644, "results": "23", "hashOfConfig": "17"}, {"size": 3944, "mtime": 1750066930829, "results": "24", "hashOfConfig": "17"}, {"size": 2056, "mtime": 1749897100773, "results": "25", "hashOfConfig": "17"}, {"size": 1159, "mtime": 1749897087436, "results": "26", "hashOfConfig": "17"}, {"size": 644, "mtime": 1750066945265, "results": "27", "hashOfConfig": "17"}, {"size": 3143, "mtime": 1750066898182, "results": "28", "hashOfConfig": "17"}, {"size": 2644, "mtime": 1750066880425, "results": "29", "hashOfConfig": "17"}, {"size": 1177, "mtime": 1750066854063, "results": "30", "hashOfConfig": "17"}, {"size": 3483, "mtime": 1750068400101, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1r8jbw0", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\Signup.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\pages\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\PrivateRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\api\\authService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\PasswordField.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\FormControlWrapper.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\OrDivider.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\GoogleOAuthButton.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\services\\googleAuthService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\config\\googleOAuth.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\sample-auth-app\\src\\components\\GoogleOAuthStatus.js", [], []]